-- Add Arabic fields to products table
ALTER TABLE products ADD COLUMN IF NOT EXISTS name_ar VARCHAR(255) DEFAULT NULL;
ALTER TABLE products ADD COLUMN IF NOT EXISTS description_ar TEXT DEFAULT NULL;
ALTER TABLE products ADD COLUMN IF NOT EXISTS details_ar TEXT DEFAULT NULL;
ALTER TABLE products ADD COLUMN IF NOT EXISTS features TEXT DEFAULT NULL;
ALTER TABLE products ADD COLUMN IF NOT EXISTS features_ar TEXT DEFAULT NULL;

-- Update existing records to have the same values for Arabic fields as English fields
UPDATE products SET name_ar = name, description_ar = description, details_ar = details WHERE name_ar IS NULL;

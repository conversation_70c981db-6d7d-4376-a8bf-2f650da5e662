<?php
declare(strict_types=1);

use Migrations\BaseMigration;

class AddFeaturesToProducts extends BaseMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/migrations/4/en/migrations.html#the-change-method
     * @return void
     */
    public function change(): void
    {
        $table = $this->table('products');
        
        // Add features column (English)
        $table->addColumn('features', 'text', [
            'default' => null,
            'null' => true,
            'comment' => 'Product features in English'
        ]);
        
        // Add features_ar column (Arabic)
        $table->addColumn('features_ar', 'text', [
            'default' => null,
            'null' => true,
            'comment' => 'Product features in Arabic'
        ]);
        
        $table->update();
    }
}

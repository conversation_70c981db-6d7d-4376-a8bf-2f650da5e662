<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Category $category
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.css') ?>">
<?php $this->end(); ?>
<div class="section-header d-flex justify-content-between align-items-center mb-3">
    <ul class="breadcrumb breadcrumb-style">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __("Dashboard") ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item"><?= __("Catalogue") ?></li>
        <li class="breadcrumb-item"><a
                href="<?= $this->Url->build(['controller' => 'Categories', 'action' => 'index']) ?>"><?= __("Categories") ?></a>
        </li>
        <li class="breadcrumb-item active"><?= __("Edit") ?></li>
    </ul>
    <a href="javascript:void(0);" class="d-flex align-items-center" id="back-button-mo" onclick="history.back();">
        <span class="rotate me-2">➥</span><small style="font-weight: bold"><?= __("BACK") ?></small>
    </a>
</div>
<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>
<div class="section-body">
    <div class="container-fluid">
        <div class="card">
            <h6 class="m-b-20" style="color: #206331"><?= __("Edit Category") ?></h6>
            <?php echo $this->Form->create($category, ['id' => 'add', 'novalidate' => true, 'type' => 'file']); ?>
            <div class="form-group row">
                <label for="name" class="col-sm-2 col-form-label fw-bold"><?= __("Category Name (English)") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('name', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'name',
                        'placeholder' => __('Category Name (English)'),
                        'label' => false,
                        'required' => true
                    ]); ?>
                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <label for="name_ar" class="col-sm-2 col-form-label fw-bold"><?= __("Category Name (Arabic)") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('name_ar', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'name_ar',
                        'placeholder' => __('Category Name (Arabic)'),
                        'label' => false,
                        'required' => true,
                        'dir' => 'rtl'
                    ]); ?>
                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <label for="description" class="col-sm-2 col-form-label fw-bold"><?= __("Description (English)") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('description', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'description',
                        'placeholder' => __('Description (English)'),
                        'label' => false
                    ]); ?>
                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <label for="description_ar" class="col-sm-2 col-form-label fw-bold"><?= __("Description (Arabic)") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('description_ar', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'description_ar',
                        'placeholder' => __('Description (Arabic)'),
                        'label' => false,
                        'dir' => 'rtl'
                    ]); ?>
                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <label for="category_icon" class="col-sm-2 col-form-label fw-bold"><?= __("Category Icon") ?> <sup
                            class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('category_icon', [
                        'type' => 'file',
                        'class' => 'form-control',
                        'id' => 'category_icon',
                        'placeholder' => __('Category Icon'),
                        'label' => false,
                        'accept' => implode(',', $iconType),
                        'required' => true
                    ]); ?>
                    <small>Only <?= implode(', ', array_map(fn($type) => '.' . $type, $categoryIconType)) ?> files are allowed. Max size: <?php echo $iconSize; ?> MB.</small>
                    <?php echo $this->Form->hidden('existing_category_icon', ['value' => !empty($category->category_icon) ? $category->category_icon : '','id'=>'existing_category_icon']); ?>
                    <div id="iconPreviewContainer">
                        <?php if (!empty($category->category_icon)):
                            $imageName = !empty($category->category_icon) ? basename($category->category_icon) : ''; ?>
                            <div class="mt-3 category-icon" style="position: relative; display: inline-block; max-width: 100px; max-height: 100px;">
                                <img src="<?php echo $category_icon; ?>" alt="<?= __("Category Icon") ?>"
                                    style="max-width: 100px; max-height: 100px;">
                                <span class="image-name" title="<?= $imageName ?>"><?= $imageName ?></span>
                                <button type="button" class="delete-img-btn" data-id="<?= $category->id; ?>">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <label for="web_banner" class="col-sm-2 col-form-label fw-bold"><?= __("Web Banner") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('web_banner', [
                        'type' => 'file',
                        'class' => 'form-control',
                        'id' => 'web_banner',
                        'placeholder' => __('Web Banner'),
                        'label' => false,
                        'accept' => implode(',', $webBannerType)
                    ]); ?>
                    <small>Only <?= implode(', ', array_map(fn($type) => '.' . $type, $categoryWebBannerType)) ?> files are allowed. Max size: <?php echo $webBannerSize; ?> MB.</small>
                    <?php echo $this->Form->hidden('existing_web_banner', ['value' => !empty($category->web_banner) ? $category->web_banner : '']); ?>
                    <div id="webPreviewContainer">
                        <?php if (!empty($category->web_banner)):
                            $imageName = !empty($category->web_banner) ? basename($category->web_banner) : ''; ?>
                            <div class="mt-3" style="position: relative; display: inline-block; max-width: 100px; max-height: 100px;">
                                <img src="<?php echo $web_banner; ?>" alt="<?= __("Web Banner") ?>"
                                    style="max-width: 100px; max-height: 100px;">
                                <span class="image-name" title="<?= $imageName ?>"><?= $imageName ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <label for="min_product_quantity" class="col-sm-2 col-form-label fw-bold"><?= __("Minimum Product Quantity") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('min_product_quantity', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'min_product_quantity',
                        'placeholder' => __('Minimum Product Quantity'),
                        'label' => false,
                    ]); ?>
                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>

            <h6 class="m-b-20" style="color: #206331"><?= __("SEO Configuration") ?></h6>
            <div class="form-group row">
                <label for="meta_title" class="col-sm-2 col-form-label fw-bold"><?= __("Meta Title") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('meta_title', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'meta_title',
                        'placeholder' => __('Meta Title'),
                        'label' => false
                    ]); ?>
                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <label for="meta_keyword" class="col-sm-2 col-form-label fw-bold"><?= __("Meta Keyword") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('meta_keyword', [
                        'type' => 'text',
                        'class' => 'form-control inputtags',
                        'id' => 'meta_keyword',
                        'placeholder' => __('Meta Keyword'),
                        'label' => false
                    ]); ?>
                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <label for="meta_description"
                    class="col-sm-2 col-form-label fw-bold"><?= __("Meta Description") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('meta_description', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'meta_description',
                        'placeholder' => __('Meta Description'),
                        'label' => false
                    ]); ?>
                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <label for="status" class="col-sm-2 col-form-label fw-bold"><?= __("Status") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('status', [
                        'type' => 'select',
                        'class' => 'form-control form-select',
                        'id' => 'status',
                        'options' => [
                            'A' => __('Active'),
                            'I' => __('Inactive')
                        ],
                        'empty' => __('Select Status'),
                        'label' => false
                    ]); ?>
                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>
            <h6 class="m-b-20" style="color: #206331"><?= __("Category Attributes") ?></h6>
            <div id="attributeTable" class="table-responsive">
                <div style="width: 90%">
                    <table class="table table-bordered mt-2" id="attribute-table">
                        <thead>
                            <tr>
                                <th><?= __("Id") ?></th>
                                <th><?= __("Attribute Name") ?></th>
                                <th><?= __("Attribute Name (Arabic)") ?></th>
                                <th><?= __("Attribute Value") ?></th>
                                <th><?= __("Attribute Value (Arabic)") ?></th>
                                <th><?= __("Position") ?></th>
                                <th><?= __("Action") ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            if (!empty($category->category_attributes)) {
                                foreach ($category->category_attributes as $attr): ?>
                                    <tr data-existing-row="true">
                                        <td class="row-id">1</td>
                                        <td>
                                            <?php echo $this->Form->control('attribute_name_id[]', [
                                                'type' => 'hidden',
                                                'class' => 'attribute-id',
                                                'value' => $attr->attribute->id
                                            ]); ?>
                                            <?php echo $this->Form->control('attribute_source[]', [
                                                'type' => 'hidden',
                                                'class' => 'attribute-source',
                                                'value' => 'datalist',
                                            ]); ?>
                                            <?php echo $this->Form->control('attribute_position[]', [
                                                'type' => 'hidden',
                                                'class' => 'attribute-position',
                                                'value' => isset($attr->position) ? $attr->position : 0,
                                            ]); ?>
                                            <?php echo $this->Form->control('attribute_name[]', [
                                                'type' => 'text',
                                                'list' => 'attribute-list',
                                                'class' => 'form-control',
                                                'placeholder' => __('Attribute Name'),
                                                'label' => false,
                                                'value' => $attr->attribute->name,
                                                'autocomplete' => 'off'
                                            ]); ?>
                                            <datalist id="attribute-list">
                                                <?php foreach ($attributes as $attribute): ?>
                                                    <option value="<?php echo h($attribute->name); ?>"
                                                        data-id="<?php echo h($attribute->id); ?>">
                                                        <?php echo h($attribute->name); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </datalist>
                                        </td>
                                        <td>
                                            <?php echo $this->Form->control('attribute_name_ar[]', [
                                                'type' => 'text',
                                                'class' => 'form-control',
                                                'placeholder' => __('Attribute Name (Arabic)'),
                                                'label' => false,
                                                'value' => isset($attr->attribute->name_ar) ? $attr->attribute->name_ar : '',
                                                'dir' => 'rtl'
                                            ]); ?>
                                        </td>
                                        <td>
                                            <?php
                                            $values = [];
                                            foreach ($attr->attribute->attribute_values as $value) {
                                                $values[] = $value->value;
                                            }

                                            $commaSeparatedValues = implode(', ', $values);
                                            ?>
                                            <input type="text" name="attribute_value[]" class="form-control attribute-value"
                                                placeholder="<?= __('Attribute Value1, Attribute Value2, Attribute Value3') ?>"
                                                value="<?= h($commaSeparatedValues) ?>" />
                                        </td>
                                        <td>
                                            <?php
                                            $values_ar = [];
                                            foreach ($attr->attribute->attribute_values as $value) {
                                                $values_ar[] = isset($value->value_ar) ? $value->value_ar : '';
                                            }

                                            $commaSeparatedValuesAr = implode(', ', $values_ar);
                                            ?>
                                            <input type="text" name="attribute_value_ar[]" class="form-control attribute-value-ar"
                                                placeholder="<?= __('Attribute Value1, Attribute Value2, Attribute Value3 (Arabic)') ?>"
                                                value="<?= h($commaSeparatedValuesAr) ?>" dir="rtl" />
                                        </td>
                                        <td>
                                            <input type="number" name="position[]" class="form-control position-input"
                                                value="<?= isset($attr->position) ? $attr->position : 0 ?>" min="0" />
                                        </td>
                                        <td>
                                            <a href="javascript:void(0);" class="move-up" data-bs-toggle="tooltip" title="<?= __('Move Up') ?>"
                                                aria-label="Move Up">
                                                <i class="fas fa-arrow-up"></i>
                                            </a>
                                            <a href="javascript:void(0);" class="move-down" data-bs-toggle="tooltip" title="<?= __('Move Down') ?>"
                                                aria-label="Move Down">
                                                <i class="fas fa-arrow-down"></i>
                                            </a>
                                            <a href="javascript:void(0);" class="add-row" data-bs-toggle="tooltip" title="<?= __('Add') ?>"
                                                aria-label="Add">
                                                <i class="fas fa-plus"></i>
                                            </a>
                                            <a href="javascript:void(0);" class="delete-attribute" data-attribute-id="<?= $attr->attribute->id ?>"
                                               data-category-id="<?= $category->id ?>" data-bs-toggle="tooltip" title="<?= __('Delete') ?>"
                                               aria-label="Delete">
                                                <i class="fas fa-minus text-danger"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach;
                            } else { ?>
                                <tr>
                                    <td class="row-id"><?= __("1") ?></td>
                                    <td>
                                        <?php echo $this->Form->control('attribute_name_id[]', [
                                            'type' => 'hidden',
                                            'class' => 'attribute-id',
                                        ]); ?>
                                        <?php echo $this->Form->control('attribute_source[]', [
                                            'type' => 'hidden',
                                            'class' => 'attribute-source',
                                            'value' => 'manual',
                                        ]); ?>
                                        <?php echo $this->Form->control('attribute_position[]', [
                                            'type' => 'hidden',
                                            'class' => 'attribute-position',
                                            'value' => 0,
                                        ]); ?>
                                        <?php echo $this->Form->control('attribute_name[]', [
                                            'type' => 'text',
                                            'list' => 'attribute-list',
                                            'class' => 'form-control',
                                            'placeholder' => __('Attribute Name'),
                                            'label' => false,
                                            'autocomplete' => 'off'
                                        ]); ?>
                                        <datalist id="attribute-list">
                                            <?php foreach ($attributes as $attribute): ?>
                                                <option value="<?php echo h($attribute->name); ?>"
                                                    data-id="<?php echo h($attribute->id); ?>">
                                                    <?php echo h($attribute->name); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </datalist>
                                    </td>
                                    <td>
                                        <?php echo $this->Form->control('attribute_name_ar[]', [
                                            'type' => 'text',
                                            'class' => 'form-control',
                                            'placeholder' => __('Attribute Name (Arabic)'),
                                            'label' => false,
                                            'dir' => 'rtl'
                                        ]); ?>
                                    </td>
                                    <td>
                                        <input type="text" name="attribute_value[]" class="form-control attribute-value"
                                            placeholder="<?= __('Attribute Value1, Attribute Value2, Attribute Value3') ?>" />
                                    </td>
                                    <td>
                                        <input type="text" name="attribute_value_ar[]" class="form-control attribute-value-ar"
                                            placeholder="<?= __('Attribute Value1, Attribute Value2, Attribute Value3 (Arabic)') ?>" dir="rtl" />
                                    </td>
                                    <td>
                                        <input type="number" name="position[]" class="form-control position-input"
                                            value="0" min="0" />
                                    </td>
                                    <td>
                                        <a href="javascript:void(0);" class="move-up" data-bs-toggle="tooltip" title="<?= __('Move Up') ?>"
                                            aria-label="Move Up">
                                            <i class="fas fa-arrow-up"></i>
                                        </a>
                                        <a href="javascript:void(0);" class="move-down" data-bs-toggle="tooltip" title="<?= __('Move Down') ?>"
                                            aria-label="Move Down">
                                            <i class="fas fa-arrow-down"></i>
                                        </a>
                                        <a href="javascript:void(0);" class="add-row" data-bs-toggle="tooltip" title="<?= __('Add') ?>"
                                            aria-label="Add">
                                            <i class="fas fa-plus"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-sm-10 offset-sm-2 text-end">
                    <button type="submit" id="btnSubmit" class="btn btn-primary"><?= __("Save") ?></button>
                </div>
            </div>
            </form>
        </div>
    </div>
</div>
<?php $this->append('script'); ?>
<script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
<script src="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js') ?>"></script>
<script type="text/javascript">
    const swalFailedTitle = "<?= addslashes(__('Error')); ?>";
    const swalInvalidFileType = "<?= addslashes(__('Invalid file type. Only ')); ?>";
    const swalFileSizeExceeded = "<?= addslashes(__('File size exceeds the maximum allowed size of')); ?>";

</script>
<script src="<?= $this->Url->webroot('js/image.js') ?>"></script>
<script>
    const form = document.getElementById('add');

    function enableFields(name) {
        const fields = form.querySelectorAll(`input[name="${name}"]`);
        fields.forEach(field => field.disabled = false);
    }

    form.addEventListener('reset', function() {
        enableFields('attribute_name[]');
        enableFields('attribute_name_ar[]');
        enableFields('attribute_value[]');
        enableFields('attribute_value_ar[]');
    });
    $('#category_icon').on('change', async function() {

        let isValid = await asyncvalidateFile(
            this,
            <?= $iconSize ?> * 1024 * 1024,
            <?= json_encode($iconType) ?>
        );

        if (isValid && this.files && this.files[0]) {
            let file = this.files[0];
            let reader = new FileReader();

            reader.onload = function(e) {
                let base64Url = e.target.result;

                let imageHtml = `
                    <div class="mt-3 banner-container" style="position: relative; display: inline-block; max-width: 100px; max-height: 100px;">
                        <img src="${base64Url}" alt="Web Banner" style="max-width: 100px; max-height: 100px;">
                        <span class="image-name" title="${file.name}">${file.name}</span>
                    </div>
                `;

                $('#iconPreviewContainer').html(imageHtml);
            };

            reader.onerror = function(error) {
                console.error('Error reading file:', error);
            };

            reader.readAsDataURL(file);
        }
    });

    $('#web_banner').on('change', async function() {

        let isValid = await asyncvalidateFile(
            this,
            <?= $webBannerSize ?> * 1024 * 1024,
            <?= json_encode($webBannerType) ?>
        );

        if (isValid && this.files && this.files[0]) {
            let file = this.files[0];
            let reader = new FileReader();

            reader.onload = function(e) {
                let base64Url = e.target.result;

                let imageHtml = `
                    <div class="mt-3 banner-container" style="position: relative; display: inline-block; max-width: 100px; max-height: 100px;">
                        <img src="${base64Url}" alt="Web Banner" style="max-width: 100px; max-height: 100px;">
                        <span class="image-name" title="${file.name}">${file.name}</span>
                    </div>
                `;

                $('#webPreviewContainer').html(imageHtml);
            };

            reader.onerror = function(error) {
                console.error('Error reading file:', error);
            };

            reader.readAsDataURL(file);
        }
    });

    $(document).on('change', 'input[list="attribute-list"]', function() {
        var selectedOption = $('#attribute-list option[value="' + $(this).val() + '"]');
        var attributeId = selectedOption.data('id');
        if (attributeId) {
            $.ajax({
                url: '<?php echo $this->Url->build('/attributes/getValues/'); ?>' + attributeId,
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    var values = Array.isArray(data) ? data : [];
                    var valuesString = values.join(', ');
                    var row = $(this).closest('tr');
                    row.find('.attribute-value').val(valuesString);
                    row.find('.attribute-id').val(attributeId);
                    row.find('.attribute-source').val('datalist');
                    // Don't disable the fields to allow editing
                    // $(this).prop('disabled', true);
                    // row.find('.attribute-value').prop('disabled', true);
                }.bind(this),
                error: function(xhr, status, error) {
                    console.error('<?= __('Error fetching attribute values:') ?>', status, error);
                }
            });
        } else {
            var row = $(this).closest('tr');
            row.find('.attribute-source').val('manual'); // Mark as manually added
        }
    });

    $(document).ready(function() {
        // Function to update row numbering and action buttons
        function updateRowActions() {
            $('table tbody tr').find('.remove-row').remove();
            $('table tbody tr').find('.add-row').remove();
            $('table tbody tr').find('.move-up, .move-down').show();

            // Update row IDs and show/hide move buttons based on position
            $('table tbody tr').each(function(index, row) {
                $(row).find('.row-id').text(index + 1);

                // Hide up arrow for first row
                if (index === 0) {
                    $(row).find('.move-up').hide();
                }

                // Hide down arrow for last row
                if (index === $('table tbody tr').length - 1) {
                    $(row).find('.move-down').hide();
                }

                // Add remove button for non-existing rows
                if (index > 0 && !$(row).data('existing-row')) {
                    $(row).find('td:last').html('<a href="javascript:void(0);" class="remove-row" data-bs-toggle="tooltip" title="<?= __('Remove') ?>" aria-label="Remove"><i class="fas fa-minus"></i></a>');
                }
            });

            // Add the "add" button to the last row
            var lastRow = $('table tbody tr:last');
            if (!lastRow.data('existing-row')) {
                lastRow.find('td:last').append(' <a href="javascript:void(0);" class="add-row" data-bs-toggle="tooltip" title="<?= __('Add') ?>" aria-label="Add"><i class="fas fa-plus"></i></a>');
            } else {
                lastRow.find('td:last').append(' <a href="javascript:void(0);" class="add-row" data-bs-toggle="tooltip" title="<?= __('Add') ?>" aria-label="Add"><i class="fas fa-plus"></i></a>');
            }

            // Update position values based on current order
            updatePositionValues();
        }

        // Function to update position values based on current row order
        function updatePositionValues() {
            $('table tbody tr').each(function(index, row) {
                // Update both the visible position input and the hidden attribute_position field
                $(row).find('.position-input').val(index + 1);
                $(row).find('input[name="attribute_position[]"]').val(index + 1);
            });
        }

        // Add a new row
        $(document).on('click', '.add-row', function() {
            var newRow = $('table tbody tr:first').clone();

            // Remove existing row marker
            newRow.removeAttr('data-existing-row');

            // Reset all input fields
            newRow.find('input').val('').prop('disabled', false);
            newRow.find('select').val('').prop('disabled', false);

            // Explicitly set important hidden fields
            newRow.find('.attribute-id').val('');
            newRow.find('.attribute-source').val('manual');

            // Set position values
            var newPosition = $('table tbody tr').length + 1;
            newRow.find('.position-input').val(newPosition);
            newRow.find('.attribute-position').val(newPosition);

            newRow.appendTo('table tbody');
            updateRowActions();

            // Log for debugging
            console.log('New row added. Position:', newPosition);
        });

        // Remove a row
        $(document).on('click', '.remove-row', function() {
            $(this).closest('tr').remove();
            updateRowActions();
        });

        // Move a row up
        $(document).on('click', '.move-up', function() {
            var currentRow = $(this).closest('tr');
            var prevRow = currentRow.prev();

            if (prevRow.length) {
                currentRow.insertBefore(prevRow);
                updateRowActions();
            }
        });

        // Move a row down
        $(document).on('click', '.move-down', function() {
            var currentRow = $(this).closest('tr');
            var nextRow = currentRow.next();

            if (nextRow.length) {
                currentRow.insertAfter(nextRow);
                updateRowActions();
            }
        });

        // Update position when position input changes
        $(document).on('change', '.position-input', function() {
            var newPosition = parseInt($(this).val());
            var currentRow = $(this).closest('tr');
            var totalRows = $('table tbody tr').length;

            // Ensure position is within valid range
            if (newPosition < 1) {
                newPosition = 1;
                $(this).val(1);
            } else if (newPosition > totalRows) {
                newPosition = totalRows;
                $(this).val(totalRows);
            }

            // Get current position (1-based)
            var currentPosition = currentRow.index() + 1;

            // If position changed, move the row
            if (newPosition !== currentPosition) {
                if (newPosition === 1) {
                    // Move to first position
                    currentRow.insertBefore($('table tbody tr:first'));
                } else {
                    // Move before the row at the target position
                    var targetRow = $('table tbody tr:eq(' + (newPosition - 1) + ')');
                    currentRow.insertBefore(targetRow);
                }

                updateRowActions();
            }
        });

        // Initialize row actions
        updateRowActions();
    });


    $(document).ready(function() {

        $('.inputtags').tagsinput({
            confirmKeys: [44]
        });

        // $(".inputtags").tagsinput("items");

        // $(".inputtags").on('keydown', function(e) {
        //     if (e.which === 13) {
        //         alert(e.which);
        //         e.preventDefault();
        //     }
        // });
    });

    // $(document).ready(function() {
    //     $.validator.addMethod("pattern", function(value, element) {
    //         return this.optional(element) || /^[a-z-]+$/.test(value);
    //     }, "<?= __("Only lowercase letters and underscores are allowed") ?>");

    //     $("#add").validate({
    //         ignore: "",
    //         rules: {
    //             'name': {
    //                 required: true
    //             },
    //             'name_ar': {
    //                 required: true
    //             },
    //             'category_icon': {
    //                 required: function() {
    //                     var existingIcon = $('input[name="existing_category_icon"]').val();
    //                     return !existingIcon;
    //                 }
    //             }
    //         },
    //         messages: {
    //             'name': {
    //                 required: "<?= __("Please enter category name in English") ?>"
    //             },
    //             'name_ar': {
    //                 required: "<?= __("Please enter category name in Arabic") ?>"
    //             },
    //             'category_icon': {
    //                 required: "<?= __("Please add category icon") ?>"
    //             }
    //         },
    //         submitHandler: function(form) {

    //             let valid = true;
    //             $('#attribute-table tbody tr').each(function() {
    //                 let attributeField = $(this).find('input[name="attribute_name[]"]');
    //                 let attributeValueField = $(this).find('input[name="attribute_value[]"]');

    //                 let attributeSelected = attributeField.val() !== '';
    //                 let attributeValueSelected = attributeValueField.val() !== '';

    //                 $(this).find('.error').remove();

    //                 if (attributeSelected && !attributeValueSelected) {
    //                     attributeValueField.after('<label class="error"><?= __("Please enter attribute values.") ?></label>');
    //                     valid = false;
    //                 }

    //                 if (!attributeSelected && attributeValueSelected) {
    //                     attributeField.after('<label class="error"><?= __("Please enter attribute name.") ?></label>');
    //                     valid = false;
    //                 }
    //             });

    //             if (!valid) {
    //                 return false;
    //             }

    //             $('button[type="submit"]').attr('disabled', 'disabled');
    //             form.submit();
    //         },
    //         errorPlacement: function(error, element) {
    //             error.appendTo(element.closest(".main-field"));
    //         },
    //         highlight: function(element) {
    //             $(element).addClass('is-invalid');
    //         },
    //         unhighlight: function(element) {
    //             $(element).removeClass('is-invalid');
    //         }
    //     });
    // });

    function validateForm() {
        let isValid = true;

        $('#add').find('input[required]').each(function() {
            if ($(this).attr('type') === 'file') {
                let fileInput = $(this);
                let existingFile = $('#existing_category_icon').val();

                if (!fileInput.val() && !existingFile) {
                    $(this).addClass('is-invalid');
                    let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                    let fieldName = $(this).closest('.form-group').find('label').text().trim();
                    fieldName = fieldName.replace(/\*$/, '');
                    feedback.text('<?= __("Please choose ") ?>' + fieldName.toLowerCase() + '.').show();
                    isValid = false;
                } else {
                    $(this).removeClass('is-invalid');
                    let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                    feedback.hide();
                }
            } else {
                let value = $(this).val().trim();

                if (value === '') {

                    $(this).addClass('is-invalid');

                    let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                    let fieldName = $(this).closest('.form-group').find('label').text().trim().replace(/\*$/, '');
                    feedback.text('<?= __("Please enter ") ?>' + fieldName.toLowerCase().trim() + '.').show();
                    isValid = false;
                } else {

                    $(this).removeClass('is-invalid');

                    let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                    feedback.hide();


                }
            }
        });

        return isValid;
    }

    $('#btnSubmit').click(function(event) {
        event.preventDefault();
        if (!validateForm()) {
            return;
        }

        // Debug form data before submission
        console.log('Form submission - Attribute rows:', $('#attribute-table tbody tr').length);
        $('#attribute-table tbody tr').each(function(index) {
            console.log('Row ' + (index + 1) + ' - Name:', $(this).find('input[name="attribute_name[]"]').val());
            console.log('Row ' + (index + 1) + ' - ID:', $(this).find('input[name="attribute_name_id[]"]').val());
            console.log('Row ' + (index + 1) + ' - Source:', $(this).find('input[name="attribute_source[]"]').val());
            console.log('Row ' + (index + 1) + ' - Existing:', $(this).attr('data-existing-row') ? 'Yes' : 'No');
        });

        var form = $('#add')[0];
        form.action = "<?= $this->Url->build(['controller' => 'Categories', 'action' => 'edit', $category->id]) ?>";
        $('#btnSubmit').attr('disabled', true);
        form.submit();
    });

    // Parent category code removed

    $('.delete-img-btn').on('click', function() {
        var $button = $(this);
        var imageId = $button.data('id');

        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'Categories', 'action' => 'deleteImage']) ?>",
            type: 'POST',
            dataType: 'json',
            data: {
                image_id: imageId
            },
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function(response) {
                if (response.status === 'success') {

                    $('#existing_category_icon').val('');
                    $('.category-icon').remove();


                    swal('Success', response.message, 'success');
                } else {
                    swal('Failed', response.message, 'error');
                }
            },
            error: function(xhr) {
                swal('Failed', 'Failed to delete image. Please try again.', 'error');
            }
        });
    });

    // Delete Category Attribute
    $(document).on('click', '.delete-attribute', function() {
        var $button = $(this);
        var attributeId = $button.data('attribute-id');
        var categoryId = $button.data('category-id');

        swal({
            title: "<?= __('Are you sure?') ?>",
            text: "<?= __('This category attribute will be marked as deleted and will not be visible in the system.') ?>",
            icon: "warning",
            buttons: ["<?= __('Cancel') ?>", "<?= __('Delete') ?>"],
            dangerMode: true,
        })
        .then((willDelete) => {
            if (willDelete) {
                $.ajax({
                    url: "<?= $this->Url->build(['controller' => 'Categories', 'action' => 'deleteCategoryAttribute']) ?>",
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        attribute_id: attributeId,
                        category_id: categoryId
                    },
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            // Remove the row from the table
                            $button.closest('tr').fadeOut('slow', function() {
                                $(this).remove();
                                updateRowActions();
                            });
                            swal("<?= __('Success') ?>", response.message, "success");
                        } else {
                            swal("<?= __('Failed') ?>", response.message, "error");
                        }
                    },
                    error: function(xhr) {
                        swal("<?= __('Failed') ?>", "<?= __('Failed to delete category attribute. Please try again.') ?>", "error");
                    }
                });
            }
        });
    });


</script>
<?php $this->end(); ?>
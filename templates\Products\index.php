<?php

/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Product> $products
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet"
    href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
<?php $this->end(); ?>
<div class="section-header">
    <ul class="breadcrumb breadcrumb-style ">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __("Dashboard") ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item"><?= __("Catalogue") ?></li>
        <li class="breadcrumb-item active"><?= __("Products") ?></li>
    </ul>
</div>
<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>
<div class="section-body" id="list">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h4><?= __("Products") ?></h4>
                <div class="card-header-form">
                    <div class="input-group">
                        <input type="text" class="form-control search-control" placeholder="<?= __("Search") ?>"
                            id="customSearchBox" />
                        <div class="input-group-btn" id="searchFilter">
                            <button class="btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <?php if ($canAdd): ?>
                            <a href="<?= $this->Url->build(['controller' => 'Products', 'action' => 'add']) ?>"
                                class="btn m-r-15">
                                <i class="fas fa-plus"></i>
                                <?= __("Add Product") ?>
                            </a>
                            <a class="btn view-upload-product-modal btn-primary admin_btn m-r-15">
                                <i class="fas fa-upload"></i>
                                <?= __('Upload Excel') ?>
                            </a>
                            <!-- <div class="dropdown">
                                <button class="btn admin_btn m-l-40 dropdown-toggle" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-download"></i> <?= __('Excel Actions') ?>
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                    <li>
                                        <a class="dropdown-item download_csv_format" href="#">
                                            <i class="fas fa-download"></i> <?= __('Download Sample Format') ?>
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item import_product_list" href="#">
                                            <i class="fas fa-upload"></i> <?= __('Import Product List') ?>
                                        </a>
                                        <input type="file" id="excelUpload" accept=".xlsx,.xls" style="display: none;" />
                                    </li>
                                </ul>
                            </div> -->
                        <?php endif; ?>

                        <button class="btn menu-toggle" type="submit">
                            <i class="fas fa-filter"></i>
                            <?= __("Filter") ?>
                        </button>
                    </div>
                </div>
            </div>
            <div id="filter-body-container">
                <div class="input-group m-l-25">
                    <div class="d-flex align-items-center">
                        <div class="form-group d-flex align-items-center m-l-20">
                            <input type="text" name="brand" id="filterBrand" placeholder="<?= __("Enter Brand") ?>"
                                class="search-control p-10 m-l-10" value="">
                        </div>
                        <div class="form-group d-flex align-items-center m-l-20">
                            <?php echo $this->Form->control('status', [
                                'type' => 'select',
                                'options' => $status,
                                'id' => 'filterStatus',
                                'class' => 'form-control form-select',
                                'label' => false,
                                'empty' => __('Filter By Status'),
                                'data' => ['bs-toggle' => 'dropdown'],
                                'aria-expanded' => 'false'
                            ]) ?>
                        </div>
                        <div class="form-group d-flex align-items-center m-l-20">
                            <select name="approvalStatus" id="approvalStatus" class="form-control form-select">
                                <option value=""><?= __("Filter By Approval Status") ?></option>
                                <option value="Approved">
                                    <?= __("Approved") ?>
                                </option>
                                <option value="Pending">
                                    <?= __("Pending") ?>
                                </option>
                                <option value="Rejected">
                                    <?= __("Rejected") ?>
                                </option>
                            </select>
                        </div>
                        <div class="form-group ms-4">
                            <button class="btn btn-primary p-10" id="filter">
                                <i class="fa fa-filter" aria-hidden="true"></i>
                            </button>
                            <button type="reset" class="btn btn-primary p-10" onclick="resetFilters()"><i
                                    class="fas fa-redo-alt"></i></button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped"
                        id="table-1">
                        <thead>
                            <tr>
                                <th><?= __("Id") ?></th>
                                <th><?= __("Product Image") ?></th>
                                <th><?= __("Product Title") ?></th>
                                <th><?= __("Product Supplier Ref ID") ?></th>
                                <th><?= __("Brand") ?></th>
                                <th><?= __("Category") ?></th>
                                <th><?= __("SKU ID") ?></th>
                                <th><?= __("Product Size") ?></th>
                                <th><?= __("Product Weight (KG)") ?></th>
                                <th><?= __("Sales Price") ?></th>
                                <th id="status"><?= __("Status") ?></th>
                                <th id="approval_status"><?= __("Approval Status") ?></th>
                                <?php if ($canView || $canEdit || $canDelete): ?>
                                    <th class="actions"><?= __("Actions") ?></th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $i = 1;
                            foreach ($products as $product): ?>
                                <tr>
                                    <td><?= $i ?></td>
                                    <td class="table-img"><?php if (!$product->product_image) { ?>
                                            <img alt="image" src="<?= $this->Url->webroot('img/user.png'); ?>"
                                                style="width: 50px; height: auto;"><?php } else { ?>
                                            <img src="<?php echo $product->product_image; ?>"
                                                style="width: 50px; height: auto;">
                                        <?php } ?>
                                    </td>
                                    <td><?= h($product->name) ?></td>
                                    <td><?= h(!empty($product->reference_name) ? $product->reference_name : '-'); ?></td>
                                    <td><?= h($product->brand->name ?? '-'); ?></td>
                                    <td>
                                        <h6 style="margin-bottom: 1px; font-size: 14px;">Category</h6>
                                        <?= !empty($product->parentCategory) ? h($product->parentCategory->name) : 'None' ?>
                                        <h6 style="margin-top: 5px;margin-bottom: 1px;font-size: 14px;">Sub Category</h6>
                                        <?= !empty($product->deepestCategory) ? h($product->deepestCategory->name) : 'None' ?>
                                    </td>
                                    <td><?= h($product->sku) ?></td>
                                    <td><?= h($product->product_size ?? '-'); ?></td>
                                    <td><?= h($product->product_weight ?? '-'); ?></td>
                                    <td>
                                        <?= h($product->sales_price ? number_format($product->sales_price, 2, $decimalSeparator, $thousandSeparator) . ' ' . '' : '-'); ?>
                                    </td>
                                    <td>
                                        <?php
                                        $statusval = $statusMap[$product->status] ?? ['label' => 'Unknown', 'class' => 'col-red'];
                                        ?>
                                        <span class="badge-outline <?= $statusval['class'] ?>">
                                            <?= h($statusval['label']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        $approvalMap = [
                                            'Pending' => ['label' => __('Pending'), 'class' => 'col-green'],
                                            'Approved' => ['label' => __('Approved'), 'class' => 'col-red'],
                                            'Rejected' => ['label' => __('Rejected'), 'class' => 'col-red']
                                        ];

                                        $approval_status = $approvalMap[$product->approval_status] ?? ['label' => __('Unknown'), 'class' => 'col-red'];
                                        ?>
                                        <span class="badge-outline <?= $approval_status['class'] ?>">
                                            <?= h($approval_status['label']) ?>
                                        </span>
                                    </td>
                                    <?php if ($canView || $canEdit || $canDelete): ?>
                                        <td class="actions">
                                            <?php if ($canView): ?>
                                                <a href="<?= $this->Url->build(['controller' => 'Products', 'action' => 'view', $product->id]) ?>"
                                                    class="" data-toggle="tooltip" title="<?= __("View") ?>"><i
                                                        class="far fa-eye m-r-10"></i></a>
                                            <?php endif; ?>
                                            <?php if ($canEdit): ?>
                                                <a href="<?= $this->Url->build(['controller' => 'Products', 'action' => 'edit', $product->id]) ?>"
                                                    class="" data-toggle="tooltip" title="<?= __("Edit") ?>"><i
                                                        class="fas fa-pencil-alt m-r-10"></i></a>
                                            <?php endif; ?>
                                            <?php if ($canDelete): ?>
                                                <a href="<?= $this->Url->build(['controller' => 'Products', 'action' => 'delete', $product->id]) ?>"
                                                    class="delete-btn" data-toggle="tooltip" title="<?= __("Delete") ?>"
                                                    data-delete-confirmation="<?= addslashes($deleteConfirmationMessage); ?>"
                                                    data-delete-warning="<?= addslashes($deleteWarningMessage); ?>"
                                                    data-delete-fail="<?= addslashes($deleteFailMessage); ?>">
                                                    <i class="far fa-trash-alt"></i>
                                                </a>
                                            <?php endif; ?>
                                            <?php if ($canEdit): ?>
                                                <a href="javascript:void(0);"
                                                    class=""
                                                    data-toggle="tooltip"
                                                    title="<?= __("Price settings") ?>"
                                                    data-target="#pricesettings_modal"
                                                    onclick="loadPriceSettingsModal(<?= $product->id ?>, this)">
                                                    <i class="fa fa-money-bill-alt m-r-10"></i>
                                                </a>
                                            <?php endif; ?>



                                            <?php if ($canApprove && $product->approval_status !== 'Approved'): ?>
                                                <a href="#" onclick="approveProduct(this, <?= h($product->id) ?>)" class="approve-btn" data-toggle="tooltip" title="<?= __("Approve") ?>"><i
                                                        class="fa fa-check m-r-10"></i></a>
                                            <?php endif; ?>
                                        </td>
                                    <?php endif; ?>
                                </tr>
                            <?php $i++;
                            endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<!-- Add Price settings Modal -->
<div class="modal fade" id="pricesettings_modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="pricesettings_modalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="pricesettings_modalLabel"><?= __('Modify Product Price') ?></h5>
            </div>
            <?php echo $this->Form->create(null, ['id' => 'priceSettingsForm', 'novalidate' => true, 'type' => 'post', 'enctype' => 'multipart/form-data']); ?>
            <div class="modal-body">
                <input type="hidden" id="product_id" name="product_id" value="">
                <input type="hidden" id="product_row" name="product_row" value="">

                <div class="form-group">
                    <label for="sale-price"><?= __('Sale Price') ?><sup class="text-danger font-11">*</sup></label>
                    <?php echo $this->Form->control('sales_price', [
                        'type' => 'number',
                        'class' => 'form-control',
                        'id' => 'sale-price',
                        'placeholder' => __('Sale price'),
                        'label' => false,
                        'required' => true,
                    ]); ?>
                </div>

                <div class="form-group">
                    <label for="promotion-price"><?= __('Promotional Price') ?></label>
                    <?php echo $this->Form->control('promotion_price', [
                        'type' => 'number',
                        'class' => 'form-control',
                        'id' => 'promotion-price',
                        'placeholder' => __('Promotional Price'),
                        'label' => false
                    ]); ?>
                </div>

                <div class="form-group">
                    <label for="latest-purcahse-price"><?= __('Latest Purchase Price') ?></label>
                    <span id="latest-purcahse-price"></span>
                </div>

                <div class="form-group">
                    <label for="latest-normal-reseller-price"><?= __('Latest Normal Reseller Price') ?></label>
                    <span id="latest-normal-reseller-price"></span>
                </div>

                <div class="form-group">
                    <label for="latest-large-reseller-price"><?= __('Latest Large Reseller Price') ?></label>
                    <span id="latest-large-reseller-price"></span>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" id="modalClass" class="btn btn-secondary" data-bs-dismiss="modal"><?= __('Close') ?></button>
                <button type="submit" class="btn" id="btnSaveProductPrice"><?= __('Save') ?></button>
            </div>
            </form>
        </div>
    </div>
</div>

<div id="upload-product-modal" style="margin-top: 100px;" class="modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" style="max-width: 900px !important;" role="document">
        <div class="modal-content m-l-70">
            <div class="modal-header">
                <div class="container">
                    <div class="d-flex">
                        <h5 class="modal-title"><?= __("Upload Products") ?></h5>
                        <a class="btn admin_btn m-l-40 download_csv_format admin_btn"><i class="fas fa-download"></i> <?= __('Download Sample Format') ?></a>
                    </div>
                </div>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-body-content">
                <div class="container">
                    <form id="upload_product_form">
                        <div class="form-group row">
                            <label for="excelUpload" class="col-sm-2 col-form-label fw-bold">
                                <?= __("Upload Excel File") ?> <sup class="text-danger font-11">*</sup>
                            </label>
                            <div class="col-sm-5 main-field">
                                <input type="file" id="excelUpload" accept=".xlsx,.xls" class="form-control" />
                                <span style="display:none;color: red;" id="upload_file_error"><?= __('Please select an .xlsx file') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="zipUpload" class="col-sm-2 col-form-label fw-bold">
                                <?= __("Upload ZIP (Images)") ?> <sup class="text-danger font-11">*</sup>
                            </label>
                            <div class="col-sm-5 main-field">
                                <input type="file" id="zipUpload" accept=".zip" class="form-control" />
                                <span style="display:none;color: red;" id="upload_zip_error"><?= __('Please select a valid ZIP file') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <div class="col-sm-10 offset-sm-2">
                                <button style="background-color: #0d839b !important;color: white !important" type="submit" class="btn upload_product_btn admin_btn">
                                    <?= __('Save') ?>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<div id="loader-uplo" class="ax-loader-overlay hidden">
    <img src="<?= $this->Url->webroot('img/loaders.gif') ?>" alt="Loading...">
</div>
<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script
    src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script src="<?= $this->Url->webroot('js/delete.js'); ?>"></script>
<script src="<?= $this->Url->webroot('js/filter.js') ?>"></script>
<script>
    var paginationCount = <?= json_encode($paginationCount) ?>;
    var table = $("#table-1").DataTable({
        buttons: [{
            extend: 'csvHtml5',
            text: '<i class="fas fa-file-download"></i>', // CSV icon
            titleAttr: 'Export CSV',
            title: 'Products',
            exportOptions: {

                columns: function(idx, data, node) {
                    return idx !== 1 && idx !== 12;
                },
                format: {
                    body: function(data, row, column, node) {
                        var textData = $(node).clone().find("h6").remove().end().text().trim();

                        var lines = textData.split("\n").map(line => line.trim()).filter(line => line !== "");

                        if (column === 4) {
                            var category = lines.length > 0 ? lines[0] : "";
                            var subCategory = lines.length > 1 ? lines[1] : "";
                            var text = 'Category : ' + category + ', ' + 'Sub Category : ' + subCategory;

                            return text;
                        }
                        if (column === 13) {
                            return lines.length > 1 ? lines[1] : "";
                        }
                        return textData;
                    }
                }
            }
        }],
        columnDefs: [{
                orderable: false,
                targets: -1
            },
            {
                className: 'table-img',
                targets: 1
            },
        ],
        dom: 'Brtip',
        pageLength: paginationCount,
        "columns": [{
                "data": "id"
            },
            {
                "data": "image"
            },
            {
                "data": "name"
            },
            {
                "data": "reference_name"
            },
            {
                "data": "brand"
            },
            {
                "data": "category"
            },
            {
                "data": "sku"
            },
            {
                "data": "size"
            },
            {
                "data": "weight"
            },
            {
                "data": "price"
            },
            {
                "data": "status"
            },
            {
                "data": "approval_status"
            },
            {
                "data": "actions",
                "render": function(data, type, row) {
                    return data;
                }
            }
        ]
    });

    $('#customSearchBox').on('keyup', function() {
        table.search(this.value).draw();
    });

    $('#filter').on('click', function(event) {
        performSearch();
    });

    function resetFilters() {
        $('#customSearchBox').val('');
        $('#filterStatus').val('');
        $('#filterBrand').val('');
        $('#approvalStatus').val('');
        table.search('').columns().search('').draw();
        table.ajax.url('<?= $this->Url->build(['controller' => 'Products', 'action' => 'filterSearch']) ?>').load();
    }

    function performSearch() {
        var filterBrand = $('#filterBrand').val();
        var filterStatus = $('#filterStatus').val();
        var approvalStatus = $('#approvalStatus').val();

        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Products', 'action' => 'filterSearch']) ?>',
            type: 'GET',
            data: {
                filterBrand: filterBrand,
                filterStatus: filterStatus,
                approvalStatus: approvalStatus
            },
            success: function(response) {
                table.clear().rows.add(response.data).draw();
            },
            error: function(xhr, status, error) {
                console.log('Error:', error);
            }
        });
    }

    function approveProduct(element, productId) {
        const swalApproveConfirmation = "<?= __("Are you sure you want to approve this product?") ?>";
        const swalApproveWarning = "<?= __("Once approved, the product will be available for customers.") ?>";

        swal({
            title: swalApproveConfirmation,
            text: swalApproveWarning,
            icon: "warning",
            buttons: true,
            dangerMode: true,
        }).then((willApprove) => {
            if (willApprove) {
                $.ajax({
                    url: '<?= $this->Url->build(['controller' => 'Products', 'action' => 'approveProduct']) ?>',
                    type: 'POST',
                    data: {
                        id: productId
                    },
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            swal("Approved!", response.message, "success");

                            const row = $(element).closest('tr');
                            const dataTable = $('#table-1').DataTable();
                            const rowIndex = row.index();
                            dataTable.cell(rowIndex, 11).data('<span class="label-sucess">Approved</span>').draw(false);
                            $(row).find('.approve-btn').remove();
                        } else {
                            swal("Error!", response.message || "There was an error approving the product.", "error");
                        }
                    },
                    error: function(xhr, status, error) {
                        swal("Error!", "There was an error approving the product.", "error");
                    }
                });
            } else {
                swal("Your product is not approved.");
            }
        });
    }

    function formatCurrency(amount, decimalSeparator, thousandSeparator) {
        amount = parseFloat(amount) || 0.00;

        var formatted = amount.toFixed(2);

        var parts = formatted.split('.');

        parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandSeparator);

        return parts.join(decimalSeparator);
    }

    function loadPriceSettingsModal(product_id, row) {
        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Products', 'action' => 'getProductPrices']) ?>',
            type: 'POST',
            data: {
                id: product_id
            },
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function(response) {
                if (response.status === 'success') {
                    $('#product_id').val(product_id);
                    $('#sale-price').val(response.data.sales_price || '0.00');
                    $('#promotion-price').val(response.data.promotion_price || '0.00');
                    var decimalSeparator = "<?= addslashes($decimalSeparator) ?>";
                    var thousandSeparator = "<?= addslashes($thousandSeparator) ?>";
                    var currencySymbol = "<?= $currencySymbol ?>";
                    var amount = formatCurrency(response.supplier_latest_price, decimalSeparator, thousandSeparator);

                    $('#latest-purcahse-price').text((amount || '0,00') + ' ' + currencySymbol);
                    $('#latest-normal-reseller-price').text((response.data.normal_reseller_price || '0,00') + ' ' + currencySymbol);
                    $('#latest-large-reseller-price').text((response.data.large_reseller_price || '0,00') + ' ' + currencySymbol);

                    let rowIndex = $(row).closest('tr').index(); // Get the row index
                    $('#product_row').val(rowIndex);
                    $('#pricesettings_modal').modal('show');
                } else {
                    swal("Error!", response.message || "There was an error fetching the product data.", "error");
                }
            },
            error: function(xhr, status, error) {
                swal("Error!", "There was an error with the request.", "error");
            }
        });
    }
    $('#btnSaveProductPrice').click(function(event) {
        event.preventDefault();
        let isValid = true;

        $('#priceSettingsForm').find('input[required], select[required]:visible').each(function() {
            let value = $(this).val().trim();

            if (value === '') {
                $(this).addClass('is-invalid');
                let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                let fieldName = $(this).closest('.form-group').find('label').text().trim().replace(/\*$/, '');
                feedback.text('<?= __("Please enter ") ?>' + fieldName.toLowerCase() + '.').show();
                isValid = false;
            } else {
                $(this).removeClass('is-invalid');
                let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                feedback.hide();
            }
        });

        if (isValid) {
            $('#btnSaveProductPrice').attr('disabled', true);
            var data = $('#priceSettingsForm').serialize();

            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'Products', 'action' => 'addProductPrices']) ?>",
                type: 'POST',
                data: data,
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        swal({
                            title: "Success!",
                            text: response.message,
                            icon: "success",
                            timer: 1000,
                            buttons: false
                        });
                        let rowIndex = $('#product_row').val();
                        let row = $('table tbody tr').eq(rowIndex);

                        row.find('td').eq(7).text(response.data);

                        $('#pricesettings_modal').modal('hide');
                        $('#btnSaveProductPrice').attr('disabled', false);
                    } else {
                        swal("Failed!", response.message, "error");
                    }
                },
                error: function(xhr, status, error) {
                    console.log(error);
                    swal("Failed!", response.message, "error");
                }
            });
        }
    });

    $('.download_csv_format').on('click', function() {
        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'Products', 'action' => 'exportToExcel']) ?>",
            type: 'GET',
            xhrFields: {
                responseType: 'blob'
            },
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function(data) {
                var a = document.createElement('a');
                var url = window.URL.createObjectURL(data);
                a.href = url;
                a.download = 'sample_product_upload_format.xlsx';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
            },
            error: function(xhr, status, error) {
                swal(
                    '<?= __('Failed') ?>',
                    '<?= __('Failed to download sample_product_upload_format file. Please try again.') ?>',
                    'error'
                );
            }
        });
    });

    $(document).on('click', '.import_product_list', function() {
        $('#excelUpload').click();
    });

    $(document).ready(function() {
        $('.upload_product_btn').on('click', function(e) {
            $('.upload_product_btn').attr('disabled', true);
            e.preventDefault();
            $('#loader-uplo').removeClass('hidden');

            const excelInput = $('#excelUpload')[0];
            const zipInput = $('#zipUpload')[0];
            const excelFile = excelInput.files[0];
            const zipFile = zipInput.files[0];

            if (!excelFile || !zipFile) {
                swal('<?= __("Failed") ?>', '<?= __("Please select both an Excel file and a ZIP file to upload.") ?>', 'error');
                $('.upload_product_btn').attr('disabled', false);
                $('#loader-uplo').addClass('hidden');
                return;
            }

            // Validate Excel File
            const excelFileName = excelFile.name;
            const excelFileExtension = excelFileName.split('.').pop().toLowerCase();
            if (!['xlsx', 'xls'].includes(excelFileExtension)) {
                swal('<?= __("Failed") ?>', '<?= __("Only Excel files (.xlsx, .xls) are allowed.") ?>', 'error');
                $('.upload_product_btn').attr('disabled', false);
                $('#loader-uplo').addClass('hidden');
                return;
            }

            // Validate ZIP File
            const zipFileName = zipFile.name;
            const zipFileExtension = zipFileName.split('.').pop().toLowerCase();
            if (zipFileExtension !== 'zip') {
                swal('<?= __("Failed") ?>', '<?= __("Only ZIP files (.zip) are allowed.") ?>', 'error');
                $('.upload_product_btn').attr('disabled', false);
                $('#loader-uplo').addClass('hidden');
                return;
            }

            // Create FormData
            const formData = new FormData();
            formData.append('excelFile', excelFile);
            formData.append('zipFile', zipFile);

            // AJAX Request
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'Products', 'action' => 'uploadExcel']) ?>",
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    if (response.status === 'success') {
                        swal('<?= __("Success") ?>', '<?= __("Files uploaded successfully!") ?>', 'success')
                            .then(function() {
                                location.reload();
                            });
                    } else {
                        swal('<?= __("Failed") ?>', response.message || '<?= __("Some rows or images could not be saved.") ?>', 'error')
                            .then(function() {
                                location.reload();
                            });

                        if (response.erroredProducts && response.erroredProducts.length > 0) {
                            const erroredProducts = response.erroredProducts;
                            let logContent = '';
                            erroredProducts.forEach(function(error) {
                                logContent += `Row Index: ${error.row_index}\n`;
                                logContent += `Data: ${JSON.stringify(error.data)}\n`;
                                logContent += `Errors: ${JSON.stringify(error.errors)}\n\n`;
                            });

                            const blob = new Blob([logContent], {
                                type: 'text/plain'
                            });
                            const link = document.createElement('a');
                            const url = URL.createObjectURL(blob);
                            link.href = url;
                            link.download = 'errored_products.txt';

                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);

                            URL.revokeObjectURL(url);
                        }
                    }
                    $('#loader-uplo').addClass('hidden');
                    $('.upload_product_btn').attr('disabled', false);
                },
                error: function() {
                    swal('<?= __("Failed") ?>', '<?= __("An error occurred while uploading the files.") ?>', 'error')
                        .then(function() {
                            location.reload();
                        });
                }
            });
        });
    });

    $('.view-upload-product-modal').on('click', function() {

        $('#upload_product_form')[0].reset();
        $('#upload-product-modal').modal('show');
    });
</script>
<?php $this->end(); ?>
<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Product $product
 * @var \App\Model\Entity\ProductAttribute $productAttribute
 */
?>

<?php $this->append('style'); ?>
<!DOCTYPE html>
<html lang="en">

<head>

    <link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
    <link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
    <link rel="stylesheet"
        href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
    <style>
        input,
        select,
        textarea {
            width: 300px;
            padding: 5px;
            margin-bottom: 10px;
        }

        #attribute-filter,
        #status-filter,
        #related-product-status-filter {
            width: 200px;
        }

        #tblAttributes,
        #tblRelatedProducts {
            width: 100% !important;
        }

        #tblAttributes th,
        #tblAttributes td,
        #tblAttributes tr,
        #tblRelatedProducts th,
        #tblRelatedProducts td,
        #tblRelatedProducts tr {
            width: 10% !important;
        }

        .filter-body-container {
            background-color: rgba(13, 131, 155, 0.04);
        }

        .filter-body-container .input-group .btn {
            box-shadow: unset !important;
        }

        .filter-body-container .btn:focus,
        .filter-body-container .btn:hover,
        .filter-body-container .btn.active {
            background-color: #f77f00 !important;
        }

        .filter-body-container {
            display: none;
            opacity: 0;
            transform: translateX(20px);
            transition: opacity 0.5s ease, transform 0.5s ease;
        }

        .filter-body-container.show {
            display: block;
            opacity: 1;
            transform: translateX(0);
        }

        .filter-body-container.showing {
            display: block;
            animation: slideFadeIn 0.5s ease forwards;
        }

        .filter-body-container.hiding {
            animation: slideFadeOut 0.5s ease forwards;
        }

        .modal-loader {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content-loader {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px;
            width: 200px;
        }

        .modal-video {
            position: fixed;
            z-index: 9999;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
        }

        .modal-content-video {
            margin: 15% auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 10px;
            max-width: 700px;
            text-align: center;
            position: relative;
        }

        .close-video {
            position: absolute;
            top: 10px;
            right: 20px;
            font-size: 30px;
            cursor: pointer;
        }

        #modalImage {
            max-width: 80%;
            max-height: 80%;
        }

        .nav-btn {
            background-color: white;
            border: none;
            padding: 10px;
            cursor: pointer;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            transition: background-color 0.3s;
        }

        .nav-btn:hover {
            background-color: #f0f0f0;
        }

        .tab-content {
            border: 1px solid teal;
        }

        .tab-content>.tab-pane {
            padding: 0px 0px !important;
        }

        .tab-head .active {
            text-align: center;
        }

        .tab-head .active {
            position: relative;
            top: 6px;
            z-index: 9;
        }

        #view-prod-general-id {
            position: relative;
            top: 2px;
            z-index: 9;
        }

        .is-invalid-select {
            border-color: #dc3545 !important;
            padding-right: calc(1.5em + .75rem);
            background-image: url('data:image/svg+xml,%3csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12" fill="none" stroke="%23dc3545"%3e%3ccircle cx="6" cy="6" r="4.5" /%3e%3cpath stroke-linejoin="round" d="M5.8 3.6h.4L6 6.5z" /%3e%3ccircle cx="6" cy="8.2" r=".6" fill="%23dc3545" stroke="none" /%3e%3c/svg%3e');
            background-repeat: no-repeat;
            background-position: right calc(.375em + .1875rem) center;
            background-size: calc(.75em + .375rem) calc(.75em + .375rem);
        }

        #product-id+.select2-container {
            width: 50% !important;
        }

        .widthStyle {
            width: 100% !important
        }

        .web-media-container {
            position: relative;
            width: 30%;
        }
    </style>
</head>
<?php $this->end(); ?>
<div class="section-header d-flex justify-content-between align-items-center mb-3">
    <ul class="breadcrumb breadcrumb-style mb-0">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Products', 'action' => 'index']) ?>">
                <?= __('Products') ?>
            </a>
        </li>
        <li class="breadcrumb-item active">
            <?= __('View') ?>
        </li>
    </ul>
    <a href="javascript:void(0);" class="d-flex align-items-center" id="back-button-mo" onclick="history.back();">
        <span class="rotate me-2">➥</span><small style="font-weight: bold"><?= __('BACK') ?></small>
    </a>
</div>

<h6 class="m-l-10 p-t-10 p-b-10" style="color: black;"> <?= __('View Product') ?></h6>

<?php echo $this->Form->create($product, ['id' => 'add-order-form', 'novalidate' => true, 'type' => 'post', 'enctype' => 'multipart/form-data']); ?>
<div>
    <div class="d-flex justify-content-between tab-head text-center" id="tab-buttons">
        <a href="#viewProducts" class="m-t-5 active tab-list" id="view-prod-general-id">
            <?= __('General') ?>
        </a>
        <a href="#viewDataProd" class="m-t-5 m-b-5 tab-list" id="view-prod-data-id">
            <?= __('Data') ?>
        </a>
        <a href="#viewAttributeProd" class="m-t-5 m-b-5 tab-list" id="view-prod-attributes-id">
            <?= __('Attributes') ?>
        </a>
        <a href="#viewMediaProd" class="m-t-5 m-b-5 tab-list" id="view-prod-media-id">
            <?= __('Media') ?>
        </a>
        <a href="#viewRelatedProd" class="m-t-5 m-b-5 tab-list" id="view-prod-related-products-id">
            <?= __('Related Products') ?>
        </a>
    </div>


    <div class="tab-content">
        <div class="tab-pane active" id="viewProducts">
            <div class="row">
                <div>
                    <div class="card mb-0 pb-0" id="view-prod-card">
                        <div class="card-body">
                            <div class="form-group row">
                                <label for="supplier-reference" class="col-sm-2 col-form-label fw-bold"><?= __('Supplier Reference Title') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p id="supplier-reference" name="supplier-reference" class="ps-5 text-dark">
                                        <?= h($product->reference_name) ?? '-'; ?>
                                    </p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="product-title" class="col-sm-2 col-form-label fw-bold"><?= __('Product Title') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p id="product-title" name="category" class="ps-5 text-dark">
                                        <?= h($product->name) ?? '-'; ?>
                                    </p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="product-title-ar" class="col-sm-2 col-form-label fw-bold"><?= __('Product Title (Arabic)') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p id="product-title-ar" name="category-ar" class="ps-5 text-dark" dir="rtl">
                                        <?= h($product->name_ar) ?? '-'; ?>
                                    </p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="category" class="col-sm-2 col-form-label fw-bold"><?= __('Category') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p id="category" name="category" class="ps-5 text-dark">
                                        <?= h($product->parentCategory->name) ?? '-'; ?>
                                    </p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="sub-category" class="col-sm-2 col-form-label fw-bold"><?= __('Sub Category') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p id="sub-category" name="sub-category" class="ps-5 text-dark">
                                        <?= h($product->subCategory ? $product->subCategory->name : '-') ?>
                                    </p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="product-size" class="col-sm-2 col-form-label fw-bold"><?= __('Product Size') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p id="product-size" name="product-size" class="ps-5 text-dark">
                                        <?= h($product->product_size) ?? '-' ?>
                                    </p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="brand" class="col-sm-2 col-form-label fw-bold"><?= __('Brand') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p id="brand" name="brand" class="ps-5 text-dark">
                                        <?= h($product->brand ? $product->brand->name : '-') ?>
                                    </p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="product-description" class="col-sm-2 col-form-label fw-bold"><?= __('Product Description') ?></label>
                                <div class="col-sm-10 ps-5">
                                    <blockquote>
                                        <?= $this->Text->autoParagraph($product->description, false); ?>
                                    </blockquote>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="product-description-ar" class="col-sm-2 col-form-label fw-bold"><?= __('Product Description (Arabic)') ?></label>
                                <div class="col-sm-10 ps-5">
                                    <blockquote dir="rtl">
                                        <?= $this->Text->autoParagraph($product->description_ar, false); ?>
                                    </blockquote>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="product-details-ar" class="col-sm-2 col-form-label fw-bold"><?= __('Product Details (Arabic)') ?></label>
                                <div class="col-sm-10 ps-5">
                                    <blockquote dir="rtl">
                                        <?= $this->Text->autoParagraph($product->details_ar, false); ?>
                                    </blockquote>
                                </div>
                            </div>

                            <!-- <div class="form-group row">
                                <label for="start-date" class="col-sm-2 col-form-label fw-bold"><?= __('Start Date') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p id="start-date" name="start-date" class="ps-5 text-dark">
                                        <?= h($product->promotion_start_date ? $product->promotion_start_date->format($dateFormat) : '') ?>
                                    </p>
                                </div>
                            </div> -->

                            <div class="form-group row">
                                <label for="product-images" class="col-sm-2 col-form-label fw-bold"><?= __('Default Product Image') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <?php if (!empty($product->product_images)): ?>
                                        <?php foreach ($product->product_images as $image): ?>
                                            <img src="<?= h($image->image_url) ?>" alt="<?= h($product->name) ?>" />
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <p>-</p>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- <div class="form-group row">
                                <label for="supplier-name" class="col-sm-2 col-form-label fw-bold"><?= __('Supplier Name') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p id="supplier-name" name="supplier-name" class="ps-5 text-dark">
                                        <?= h($product->supplier ? $product->supplier->name : '-') ?>
                                    </p>
                                </div>
                            </div> -->

                            <h5><?= __('SEO Configuration') ?></h5>

                            <div class="form-group row">
                                <label for="meta-title" class="col-sm-2 col-form-label fw-bold"><?= __('Meta Title') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p id="meta-title" name="meta-title" class="ps-5 text-dark">
                                        <?= h($product->meta_title) ?? '-'; ?>
                                    </p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="meta-keywords" class="col-sm-2 col-form-label fw-bold"><?= __('Meta Keywords') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p id="meta-keywords" name="meta-keywords" class="ps-5 text-dark">
                                        <?= h($product->meta_keyword) ?? '-'; ?>
                                    </p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="meta-description" class="col-sm-2 col-form-label fw-bold"><?= __('Meta Description') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p id="meta-description" name="meta-description" class="ps-5 text-dark">
                                        <?= h($product->meta_description) ?? '-'; ?>
                                    </p>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="tab-pane" id="viewDataProd">
            <div class="tab-pane active" id="viewProducts">
                <div class="row">
                    <div>
                        <div class="card mb-0 pb-0" id="view-prod-card">
                            <div class="card-body">
                                <div class="form-group row">
                                    <label for="sku-id" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('SKU ID') ?></label>
                                    <div class="col-sm-5 ps-5">
                                        <p id="sku-id" name="sku-id" class="ps-5 text-dark">
                                            <?= h($product->sku) ?? '-'; ?>
                                        </p>
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label for="barcode" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Bar Code') ?></label>
                                    <div class="col-sm-5 ps-5">
                                        <p id="barcode" class="ps-5 text-dark">
                                            <?php if (empty($product->scanned_barcode)) { ?>
                                                <?php if (!empty($barcodeImg)) : ?>
                                                    <img src="<?= h($barcodeImg) ?>" alt="<?= __("Barcode") ?>" />
                                                <?php else : ?>
                                                    <?= __('-') ?>
                                                <?php endif; ?>
                                            <?php } else { ?>
                                                <svg id="barcode-svg"></svg>
                                            <?php } ?>
                                        </p>
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label for="qrcode" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('QR Code') ?></label>
                                    <div class="col-sm-5 ps-5">
                                        <p id="qrcode" class="ps-5 text-dark">
                                            <?php if (!empty($qrcodeImg)) : ?>
                                                <img src="<?= h($qrcodeImg) ?>" alt="<?= __("QR Code") ?>" height="100px" />
                                            <?php else : ?>
                                                <?= __('-') ?>
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                </div>



                                <div class="form-group row">
                                    <label for="product-tags" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Product Tags') ?></label>
                                    <div class="col-sm-5 ps-5">
                                        <p id="product-tags" name="product-tags" class="ps-5 text-dark">
                                            <?= h($product->product_tags) ?? '-'; ?>
                                        </p>
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label for="product-size" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Product Size') ?></label>
                                    <div class="col-sm-5 ps-5">
                                        <p id="product-size" name="product-size" class="ps-5 text-dark">
                                            <?= h($product->product_size) ?? '-'; ?>
                                        </p>
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label for="product-weight" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Product Weight') ?></label>
                                    <div class="col-sm-5 ps-5">
                                        <p id="product-weight" name="product-size" class="ps-5 text-dark">
                                            <?= h($product->product_weight) ?? '-'; ?>&nbsp;<span id="measurement"><?= __('KG') ?></span>
                                        </p>
                                    </div>
                                </div>

                                <!-- <div class="form-group row">
                                    <label for="start-date" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Start Date') ?></label>
                                    <div class="col-sm-5 ps-5">
                                        <p id="start-date" name="start-date" class="ps-5 text-dark">
                                            <?= h($product->promotion_start_date ? $product->promotion_start_date->format($dateFormat) : '') ?>
                                        </p>
                                    </div>
                                </div> -->

                                <div class="form-group row">
                                    <label for="status" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Status') ?></label>
                                    <div class="col-sm-5 ps-5">
                                        <p id="status" name="status" class="ps-5 text-dark">
                                            <?= h($product->status) ? $statuses[$product->status] : '-'; ?>
                                        </p>
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label for="widget-config" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Widget Configuration') ?></label>
                                    <div class="col-sm-5 ps-5">
                                        <p id="widget-config" name="widget-config" class="ps-5 text-dark">
                                            <?= h($product->product_preference) ? $product_preferences[$product->product_preference] : '-'; ?>
                                        </p>
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label for="deal-check" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Is in Active Deals ?') ?></label>
                                    <div class="col-sm-5 ps-5">
                                        <p id="deal-check" name="deal-check" class="ps-5 text-dark">
                                            <?= !empty($product->product_deals) ? 'Yes' : 'No'; ?>
                                        </p>
                                    </div>
                                </div>




                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="tab-pane section-body" id="viewAttributeProd">
            <div class="container-fluid p-b-10">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4><?= __('Manage Product Attributes') ?></h4>
                        <div class="card-header-form d-flex align-items-center">
                            <form class="d-flex align-items-center m-l-10">
                                <div class="input-group me-2">
                                    <input type="text" class="form-control search-control" placeholder="<?= __('Search') ?>"
                                        id="customSearchBoxAttribute" />
                                    <div class="input-group-btn">
                                        <button class="btn btn-primary" type="submit">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                    <?php if ($canAdd): ?>
                                        <a class="btn btn-primary" style="
                                            position: relative;
                                            left: 10px;
                                            height:10%
                                        " data-bs-toggle="modal" data-bs-target="#addProductAttributeModal">
                                            <i class="fas fa-plus"></i>
                                            <?= __('Add Attribute') ?>
                                        </a>
                                    <?php endif; ?>
                                    <button class="btn menu-toggle fw-bold" type="submit" style="
                                            position: relative;
                                            left: 26px;
                                            height:10%;
                                        ">
                                        <i class="fas fa-filter"></i>
                                        <?= __('Filter') ?>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div id="filter-body-container">
                        <div class="input-group m-l-25">
                            <form accept-charset="utf-8" class="form-inline filter-rating attribute" id="filter-search">
                                <div class="d-flex">
                                    <div class="form-group d-flex align-items-center m-l-20">
                                        <?php echo $this->Form->control('value', [
                                            'type' => 'select',
                                            'options' => $status,
                                            'id' => 'attribute-filter',
                                            'class' => 'form-control form-select p-10',
                                            'label' => false,
                                            'empty' => __('Attribute Status'),
                                            'data' => ['bs-toggle' => 'dropdown'],
                                            'aria-expanded' => 'false'
                                        ]) ?>
                                    </div>
                                    <div class="form-group ms-4" style="margin-top:0.5%">
                                        <button type="submit" id="btnFilterAttribute" class="btn btn-primary p-10">
                                            <i class="fa fa-filter" aria-hidden="true"></i>
                                        </button>
                                        <button type="reset" id="btnResetAttribute" class="btn btn-primary p-10">
                                            <i class="fas fa-redo-alt"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                            <hr />
                        </div>
                    </div>

                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="tblAttributes" role="grid">
                                <thead>
                                    <tr role="row">
                                        <th><?= __('Attributes Name') ?></th>
                                        <th><?= __('Attribute Value') ?></th>
                                        <th><?= __('Status') ?></th>
                                        <th><?= __('Action') ?></th>
                                    </tr>
                                </thead>
                                <tbody>


                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

  
        <div class="tab-pane section-body" id="viewMediaProd">
            <div class="container-fluid p-b-10">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4><?= __('Image(s)') ?></h4>
                        <div class="card-header-form d-flex align-items-center"></div>
                    </div>
                    <form class="d-flex align-items-center m-l-10">
                        <div class="card-body">
                            <!-- Product Images Section -->
                            <h5 class="mt-3"><?= __('Product Image(s)') ?></h5>
                            <div class="row mb-3">
                                <div class="col-2">
                                    <label for="product_images"><?= __('Product Images') ?></label>
                                </div>
                                <div class="col-8">
                                    <div id="product-images-div"></div>
                                </div>
                                <div class="col-2">
                                    <?php if ($canAdd): ?>
                                        <button type="button" class="btn btn-primary" id="add-product-images"><?= __('Add Product Images') ?></button>
                                    <?php endif; ?>
                                    <!-- Hidden File Input for Product Images -->
                                    <input type="file" id="product-image-input" class="d-none" accept="image/*" multiple>
                                </div>
                            </div>



                            <!-- Product Videos Section -->
                            <h5 class="mt-3"><?= __('Product Video(s)') ?></h5>
                            <div class="row mb-3">
                                <div class="col-2">
                                    <label for="product_videos"><?= __('Product Videos') ?></label>
                                </div>
                                <div class="col-8">
                                    <div id="product-videos-div"></div>
                                </div>
                                <div class="col-2">
                                    <?php if ($canAdd): ?>
                                        <button type="button" class="btn btn-primary" id="add-product-videos"><?= __('Add Product Videos') ?></button>
                                    <?php endif; ?>
                                    <!-- Hidden File Input for Product Videos -->
                                    <input type="file" id="product-video-input" class="d-none" accept="video/*" multiple>
                                </div>
                            </div>


                        </div>
                    </form>
                </div>
            </div>
        </div>


        <div class="tab-pane section-body" id="viewRelatedProd">
            <div class="container-fluid p-b-10">
                <div class="card pt-0">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4><?= __('Manage Related Products') ?></h4>
                        <div class="card-header-form d-flex align-items-center">
                            <form class="d-flex align-items-center m-l-10">
                                <div class="input-group me-2">
                                    <input type="text" class="form-control search-control" placeholder="<?= __('Search') ?>"
                                        id="customSearchBoxRelated" />
                                    <div class="input-group-btn">
                                        <button class="btn btn-primary" type="submit">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                    <button class="btn relatedprod fw-bold m-0" type="submit" style="height:33px">
                                        <i class="fas fa-filter"></i>
                                        <?= __('Filter') ?>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="card-header p-0">
                        <div class="d-flex col-sm-9 align-items-center">
                            <label for="product-id" class="m-l-25"><?= __('Choose Products') ?></label>
                            <div class="col-sm-9">
                                <?php echo $this->Form->control('product.id', [
                                    'type' => 'select',
                                    'id' => 'product-id',
                                    'options' => $products,
                                    'class' => ' form-control select2 widthStyle',
                                    'label' => false,
                                    // 'empty' => __('All'),
                                    'multiple' => 'multiple'
                                ]) ?>
                            </div>
                        </div>
                    </div>
                    <div class="card-header m-l-0 p-l-0 m-t-10">
                        <?php if ($canAdd): ?>
                            <button type="button" id="addRelatedProductbtn" class="btn" onclick="addRelatedProduct()">
                                <i class="fas fa-plus"></i>
                                <?= __('Add Related Product') ?>
                            </button>
                        <?php endif; ?>
                    </div>
                    <div id="filter-body-container-relatedprod" class="filter-body-container">
                        <div class="input-group m-l-25">
                            <form accept-charset="utf-8" class="form-inline filter-rating related" id="filter-search-related">
                                <div class="d-flex">
                                    <div class="form-group d-flex align-items-center m-l-20">
                                        <?php echo $this->Form->control('status', [
                                            'type' => 'select',
                                            'options' => $status,
                                            'id' => 'related-product-status-filter',
                                            'class' => 'form-control form-select p-10',
                                            'label' => false,
                                            'empty' => __('Status'),
                                            'data' => ['bs-toggle' => 'dropdown'],
                                            'aria-expanded' => 'false'
                                        ]) ?>
                                    </div>
                                    <div class="form-group ms-4" style="margin-top:0.5%">
                                        <button type="submit" id="btnFilterRelated" class="btn btn-primary p-10">
                                            <i class="fa fa-filter" aria-hidden="true"></i>
                                        </button>
                                        <button type="reset" id="btnResetRelated" class="btn btn-primary p-10">
                                            <i class="fas fa-redo-alt"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                            <hr />
                        </div>
                    </div>

                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="tblRelatedProducts" role="grid">
                                <thead>
                                    <tr role="row">
                                        <th><?= __('Related Product ID') ?></th>
                                        <th><?= __('Product Name') ?></th>
                                        <th><?= __('SKU ID') ?></th>
                                        <th><?= __('Supplier Reference Title') ?></th>
                                        <th><?= __('Status') ?></th>
                                        <th><?= __('Action') ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>
</div>
</form>
</div>
<!-- Add Product Attribute Modal -->
<div class="modal fade" id="addProductAttributeModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="addProductAttributeModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addProductAttributeModalLabel"><?= __('Add Product Attribute') ?></h5>
            </div>
            <?php echo $this->Form->create(null, ['id' => 'addProductAttributeForm', 'novalidate' => true, 'type' => 'post', 'enctype' => 'multipart/form-data']); ?>
            <div class="modal-body">
                <input type="hidden" id="hidden-attribute-id" name="product_attribute_id" value="">
                <input type="hidden" name="product_id" value="<?= $product->id ?>">
                <div class="form-group">
                    <label for="attribute-id"><?= __('Attribute Name') ?><sup class="text-danger font-11">*</sup></label>
                    <?php echo $this->Form->control('attribute_id', [
                        'type' => 'select',
                        'options' => $prod_attributes,
                        'id' => 'attribute-id',
                        'class' => 'form-control p-10 select2',
                        'label' => false,
                        'empty' => __('Select an Attribute Name'),
                        'required' => true
                    ]) ?>
                </div>
                <div class="form-group">
                    <label for="attributeName"><?= __('Attribute Value') ?><sup class="text-danger font-11">*</sup></label>
                    <?php echo $this->Form->control('attribute_value_id', [
                        'type' => 'select',
                        'id' => 'attributeName',
                        'class' => 'form-control p-10 select2',
                        'label' => false,
                        'empty' => __('Select an Attribute Value'),
                        'required' => true,
                        'multiple' => 'multiple'
                    ]) ?>
                </div>
                <div class="form-group">
                    <label for="ckeditor"><?= __("Attribute Description") ?></label>
                    <?php echo $this->Form->control('attribute_description', [
                        'type' => 'textarea',
                        'class' => 'form-control ckeditor-textarea',
                        'id' => 'ckeditor',
                        'label' => false,
                        'required' => true
                    ]); ?>
                </div>
                <div class="form-group" id="attribute-status-input" style="display: none;">
                    <label for="attribute-status"><?= __("Attribute Status") ?><sup class="text-danger font-11">*</sup></label>
                    <?php echo $this->Form->control('status', [
                        'type' => 'select',
                        'options' => $status,
                        'id' => 'attribute-status',
                        'class' => 'form-control form-select p-10',
                        'label' => false,
                        'empty' => __('Select an Attribute Status'),
                        'required' => true
                    ]) ?>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" id="modalClass" class="btn btn-secondary" data-dismiss="modal" onclick="resetProductAttributeForm()"><?= __('Close') ?></button>
                <button type="submit" class="btn" id="btnSaveProductAttributes"><?= __('Save') ?></button>
            </div>
            </form>
        </div>
    </div>
</div>

<div class="modal fade" id="viewProductAttributeModal" tabindex="-1" aria-labelledby="viewProductAttributeModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewProductAttributeModalLabel"><?= __('View Product Attribute') ?></h5>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="viewAttributeName"><?= __('Attribute Name:') ?></label>
                    <p id="viewAttributeName"></p>
                </div>
                <div class="form-group">
                    <label for="viewAttributeValue"><?= __('Attribute Value:') ?></label>
                    <p id="viewAttributeValue"></p>
                </div>
                <div class="form-group">
                    <label for="viewAttributeDescription"><?= __('Attribute Description:') ?></label>
                    <blockquote id="viewAttributeDescription"></blockquote>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?= __('Close') ?></button>
            </div>
        </div>
    </div>
</div>



<div class="modal fade" id="viewProductVariantModal" tabindex="-1" aria-labelledby="viewProductVariantModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewProductVariantModalLabel"><?= __('View Product Variant') ?></h5>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="viewVariantName"><?= __('Variant Name') ?>:</label>
                    <p id="viewVariantName"></p>
                </div>
                <div class="form-group">
                    <label for="viewVariantSKU"><?= __('Variant SKU') ?>:</label>
                    <p id="viewVariantSKU"></p>
                </div>
                <div class="form-group">
                    <label for="viewVariantSize"><?= __('Variant Size') ?>:</label>
                    <p id="viewVariantSize"></p>
                </div>
                <div class="form-group">
                    <label for="viewVariantWeight"><?= __('Variant Weight') ?></label>
                    <p id="viewVariantWeight"></p>
                </div>
                <div class="form-group">
                    <label for="viewVariantDescription"><?= __('Variant Description') ?>:</label>
                    <blockquote id="viewVariantDescription"></blockquote>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?= __('Close') ?></button>
            </div>
        </div>
    </div>
</div>

<div id="loadingModal" class="modal modal-loader">
    <div class="modal-content-loader">
        <p><?= __('Validating, please wait...') ?></p>
    </div>
</div>

<div id="videoModal" class="modal modal-video" style="display:none;">
    <div class="modal-content-video">
        <span id="closeModal-video" class="close-video">&times;</span>
        <video id="modalVideo" width="600" controls>
            <source src="" type="video/mp4">
            <?= __('Your browser does not support the video tag.') ?>
        </video>
    </div>
</div>

<div class="modal fade" id="imageViewerModal" tabindex="-1" aria-labelledby="imageViewerModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="<?= __('Close') ?>" id="closeModal-image"></button>
            </div>
            <div class="modal-body d-flex justify-content-between align-items-center">
                <button id="prevVarImageBtn" style="display: none;" class="nav-btn" aria-label="<?= __('Previous Image') ?>">&lt;</button>
                <button id="prevImageBtn" style="display: none;" class="nav-btn" aria-label="<?= __('Previous Image') ?>">&lt;</button>
                <img id="modalImage" src="" alt="<?= __('Product Image') ?>" class="modal-image">
                <button id="nextVarImageBtn" style="display: none;" class="nav-btn" aria-label="<?= __('Next Image') ?>">&gt;</button>
                <button id="nextImageBtn" style="display: none;" class="nav-btn" aria-label="<?= __('Next Image') ?>">&gt;</button>
            </div>
        </div>
    </div>
</div>



<!-- Add Price settings Modal -->
<div class="modal fade" id="pricesettings_modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="pricesettings_modalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="pricesettings_modalLabel"><?= __('Modify Product Price') ?></h5>
            </div>
            <?php echo $this->Form->create(null, ['id' => 'priceSettingsForm', 'novalidate' => true, 'type' => 'post', 'enctype' => 'multipart/form-data']); ?>
            <div class="modal-body">
                <input type="hidden" id="product_var_id" name="product_var_id" value="">

                <div class="form-group">
                    <label for="sale-price"><?= __('Sale Price') ?><sup class="text-danger font-11">*</sup></label>
                    <?php echo $this->Form->control('sales_price', [
                        'type' => 'number',
                        'class' => 'form-control',
                        'id' => 'sale-price',
                        'placeholder' => __('Sale price'),
                        'label' => false,
                        'required' => true,
                    ]); ?>
                </div>

                <div class="form-group">
                    <label for="promotion-price"><?= __('Promotional Price') ?></label>
                    <?php echo $this->Form->control('promotion_price', [
                        'type' => 'number',
                        'class' => 'form-control',
                        'id' => 'promotion-price',
                        'placeholder' => __('Promotional Price'),
                        'label' => false
                    ]); ?>
                </div>

                <div class="form-group">
                    <label for="latest-purcahse-price"><?= __('Latest Purchase Price') ?></label>
                    <span id="latest-purcahse-price"></span>
                </div>

                <div class="form-group">
                    <label for="latest-normal-reseller-price"><?= __('Latest Normal Reseller Price') ?></label>
                    <span id="latest-normal-reseller-price"></span>
                </div>

                <div class="form-group">
                    <label for="latest-large-reseller-price"><?= __('Latest Large Reseller Price') ?></label>
                    <span id="latest-large-reseller-price"></span>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" id="modalClass" class="btn btn-secondary" data-bs-dismiss="modal"><?= __('Close') ?></button>
                <button type="submit" class="btn" id="btnSaveProductPrice"><?= __('Save') ?></button>
            </div>
            </form>
        </div>
    </div>
</div>

<div class="modal fade" id="variantImageModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="variantImageModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="variantImageModalLabel"><?= __('Add Variant Images') ?></h5>
            </div>
            <div class="modal-body">
                <?php echo $this->Form->create(null, ['id' => 'addVariantImageForm', 'novalidate' => true, 'type' => 'post', 'enctype' => 'multipart/form-data']); ?>
                <div class="form-group">
                    <label for="variant-select"><?= __('Select Variant') ?><sup class="text-danger font-11">*</sup></label>
                    <?php echo $this->Form->control('variant_id', [
                        'type' => 'select',
                        // 'options' => $partners,
                        'id' => 'variant-select',
                        'class' => 'form-control select2',
                        'label' => false,
                        'empty' => __('Select a Product Variant'),
                        'required' => true
                    ]); ?>
                    <div class="invalid-feedback">
                    </div>
                </div>
                <div class="mb-3">
                    <label for="media-variant-image" class="form-label"><?= __('Choose Image') ?><sup class="text-danger font-11">*</sup></label>
                    <input type="file" class="form-control" id="media-variant-image" name="variant_image" accept="image/*" multiple required>
                    <span id="Image-validations-media" class="Image-validations"><small>Only <?= implode(', ', $productImageTypeDisp) ?> files are allowed. Max size: <?= $ImageSize ?> MB. Dimensions: <?= $ImageMinWidth ?> x <?= $ImageMinHeight ?> and <?= $ImageMaxWidth ?> x <?= $ImageMaxHeight ?>.</small></span>
                    <div class="variant-web-media-container">

                    </div>
                    <div class="invalid-feedback">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="resetVariantImagesForm()"><?= __('Close') ?></button>
                    <button type="submit" class="btn" id="saveVariantImages" style="display: none;"><?= __('Save Images') ?></button>
                    <button type="submit" class="btn" id="saveVariantVideos" style="display: none;"><?= __('Save Videos') ?></button>
                </div>
                <?php echo $this->Form->end(); ?>
            </div>
        </div>
    </div>
</div>

<?php $this->append('script'); ?>
<script type="text/javascript">
    const swalFailedTitle = "<?= addslashes(__('Error')); ?>";
    const swalInvalidFileType = "<?= addslashes(__('Invalid file type. Only ')); ?>";
    const swalFileSizeExceeded = "<?= addslashes(__('File size exceeds the maximum allowed size of')); ?>";
    const swalInvalidDimensions = "<?= addslashes(__('Image dimensions should be between')); ?>";
</script>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/ckeditor/ckeditor.js') ?>"></script>
<script src="<?= $this->Url->webroot('js/filter.js'); ?>"></script>
<script src="<?= $this->Url->webroot('js/delete.js'); ?>"></script>
<script src="<?= $this->Url->webroot('js/images.js'); ?>"></script>
<script src="<?= $this->Url->webroot('js/image.js'); ?>"></script>
<script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.0/dist/JsBarcode.all.min.js"></script>
<script>
    $(document).ready(function() {
        let barcodeValue = "<?= h($product->scanned_barcode) ?>"; // Get barcode from PHP

        if (barcodeValue) {
            JsBarcode("#barcode-svg", barcodeValue, {
                format: "CODE128",
                displayValue: false, // Hide text below barcode
                lineColor: "#000",
                width: 2,
                height: 50
            });
        }
    });
    $(document).ready(function() {

        $('#tab-buttons a').on('click', function(e) {
            e.preventDefault();

            $('#tab-buttons a').removeClass('active');
            $('.tab-pane').removeClass('active');

            $(this).addClass('active');
            $($(this).attr('href')).addClass('active');

            var target = $(this).attr('href');
            if (target === '#viewAttributeProd') {
                var attribute_Table = $("#tblAttributes").DataTable();
                attribute_Table.ajax.reload(null, false);
                // resetProductAttributeForm();
            } else if (target === '#viewMediaProd') {
                loadProductVariantImages();
                resetVariantImagesForm();
                loadProductImages();
                loadProductVariantVideos();
                loadProductVideos();
            } else if (target === '#viewRelatedProd') {
                var related_prod_table = $("#tblRelatedProducts").DataTable();
                related_prod_table.ajax.reload(null, false);
                $('#product-id').val(null).trigger('change');
                loadAvailableProducts();
            } else if (target === '#viewPaymentSettings') {
                var payment_settings_Table = $("#tblPaymentSettings").DataTable();
                payment_settings_Table.ajax.reload(null, false);
                resetPaymentSettingsForm();
            }
        });

        $('#paymentSettingsModal').on('shown.bs.modal', function() {
            $('#partner-id').select2({
                dropdownParent: $('#paymentSettingsModal')
            });
            $('#payment-terms').select2({
                dropdownParent: $('#paymentSettingsModal')
            });
        });

        $('#addProductAttributeModal').on('shown.bs.modal', function() {
            $('#attribute-id').select2({
                dropdownParent: $('#addProductAttributeModal')
            });
            $('#attributeName').select2({
                dropdownParent: $('#addProductAttributeModal')
            });
        });
        $('.select2').select2({
            minimumResultsForSearch: 0
        });

        $('#variantImageModal').on('shown.bs.modal', function() {
            $('#variant-select').select2({
                dropdownParent: $('#variantImageModal')
            });
        });

        $(function() {
            CKEDITOR.replace("ckeditor");
            CKEDITOR.config.height = 300;

            for (instance in CKEDITOR.instances) {
                CKEDITOR.instances[instance].on('blur', function() {
                    CKEDITOR.instances[this.name].updateElement();
                });
            }
        });

        $(function() {
            CKEDITOR.replace("ckeditor-variant");
            CKEDITOR.config.height = 300;

            for (instance in CKEDITOR.instances) {
                CKEDITOR.instances[instance].on('blur', function() {
                    CKEDITOR.instances[this.name].updateElement();
                });
            }
        });

        $(function() {
            CKEDITOR.replace("ckeditor-credit-terms");
            CKEDITOR.config.height = 300;

            for (instance in CKEDITOR.instances) {
                CKEDITOR.instances[instance].on('blur', function() {
                    CKEDITOR.instances[this.name].updateElement();
                });
            }
        });

        var paginationCount = <?= json_encode($paginationCount) ?>;

        var productId = <?= json_encode($product->id) ?>;
        var statusMap = <?= json_encode($statusMap) ?>;
        var attributeTable = $("#tblAttributes").DataTable({
            ajax: {
                url: "<?= $this->Url->build(['controller' => 'ProductAttributes', 'action' => 'getAttributesData']) ?>",
                type: "GET",
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                data: function(d) {
                    d.product_id = productId;
                },
                dataSrc: function(result) {
                    if (!result || !result.attributes) {
                        return [];
                    }
                    return Object.values(result.attributes);
                }
            },
            columns: [{
                    title: "Attributes Name",
                    data: "attribute_name"
                },
                {
                    title: "Attribute Value",
                    data: "values"
                },
                {
                    title: "Status",
                    data: "status",
                    render: function(data, type, row) {
                        var statusval = statusMap[data] || {
                            label: "Unknown",
                            class: "label-danger"
                        };
                        return `
                    <div class="badge-outline ${statusval.class}">
                        ${statusval.label}
                    </div>
                `;
                    }
                },
                {
                    title: "Action",
                    data: null,
                    orderable: false,
                    render: function(data, type, row) {
                        let actions = '';

                        <?php if ($canView): ?>
                            actions += `
        <a href="#" onclick="viewProductAttribute(${productId}, ${row.attribute_id})"
            class="" data-bs-toggle="tooltip" title="<?= __('View') ?>">
            <i class="far fa-eye"></i>
        </a>`;
                        <?php endif; ?>

                        <?php if ($canEdit): ?>
                            actions += `
        <a href="#" onclick="editProductAttribute(${productId}, ${row.attribute_id})"
            class="" data-bs-toggle="tooltip" title="<?= __('Edit') ?>">
            <i class="fas fa-pencil-alt"></i>
        </a>`;
                        <?php endif; ?>

                        <?php if ($canDelete): ?>
                            actions += `
       <a href="<?= $this->Url->build([
                                'controller' => 'ProductAttributes',
                                'action' => 'deleteProductAttributes'
                            ]) ?>?product_id=${productId}&attribute_id=${row.attribute_id}"
            class="delete-btn" data-bs-toggle="tooltip" title="<?= __('Delete') ?>"
            data-id="${row.attribute_id}"
            data-delete-confirmation="<?= addslashes($deleteConfirmationMessage); ?>"
            data-delete-warning="<?= addslashes($deleteWarningMessage); ?>"
            data-delete-fail="<?= addslashes($deleteFailMessage); ?>">
            <i class="far fa-trash-alt"></i>
        </a>`;
                        <?php endif; ?>

                        return actions;
                    }
                }
            ],
            columnDefs: [{
                orderable: false,
                targets: [-1],
                width: '200px'
            }],
            order: [],
            dom: "rtip",
            pageLength: paginationCount,
            language: {
                infoFiltered: "",
            }
        });


        $("#customSearchBoxAttribute").on("keyup", function() {
            attributeTable.search(this.value).draw();
        });

        $("#btnResetAttribute").on("click", function() {
            attributeTable.search('').columns().search('').draw();
        });

        $("#btnFilterAttribute").on("click", function(event) {
            event.preventDefault();
            var attributefilter = $("#attribute-filter").val();
            attributeTable.column(2).search(attributefilter, true, false, false).draw();
            attributeTable.draw();
        });


        $('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
            attributeTable.columns.adjust().draw();
        });

        $('#addProductAttributeModal').on('hidden.bs.modal', function() {
            resetProductAttributeForm();
        });


        $('#btnSaveProductAttributes').click(function(event) {
            event.preventDefault();
            let isValid = true;

            $('#addProductAttributeForm').find('input[required], select[required]:visible').each(function() {
                // let value = $(this).val().trim();
                let isSelect2 = $(this).hasClass('select2');
                if (isSelect2) {
                    value = $(this).val();
                } else {
                    value = $(this).val() ? $(this).val().trim() : '';
                }
                if ((value.length === 0) || value === '') {
                    $(this).addClass('is-invalid');
                    let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                    let fieldName = $(this).closest('.form-group').find('label').text().trim().replace(/\*$/, '');
                    feedback.text('<?= __("Please enter ") ?>' + fieldName.toLowerCase() + '.').show();
                    isValid = false;
                    if (isSelect2) {
                        $(this).closest('.form-group').find('.select2-selection--single').addClass('is-invalid-select');
                        $(this).closest('.form-group').find('.select2-selection--multiple').addClass('is-invalid-select');
                    }
                } else {
                    $(this).removeClass('is-invalid');
                    let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                    feedback.hide();
                    if (isSelect2) {
                        $(this).closest('.form-group').find('.select2-selection--single').removeClass('is-invalid-select');
                        $(this).closest('.form-group').find('.select2-selection--multiple').removeClass('is-invalid-select');
                    }
                }
            });

            if (isValid) {
                $('#btnSaveProductAttributes').attr('disabled', true);
                var product_id = <?= json_encode($product->id) ?>;
                if (CKEDITOR.instances['ckeditor']) {
                    CKEDITOR.instances['ckeditor'].updateElement();
                }
                var data = $('#addProductAttributeForm').serialize();
                let url = "<?= $this->Url->build(['controller' => 'ProductAttributes', 'action' => 'addProductAttribute']) ?>";
                if ($('#hidden-attribute-id').val()) {
                    var attr_id = $('#hidden-attribute-id').val();
                    url = "<?= $this->Url->build(['controller' => 'ProductAttributes', 'action' => 'editProductAttribute']) ?>/" + product_id + '/' + attr_id;
                } else {
                    data += '&status=A';
                }
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: data,
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.status === 'success') {
                            swal({
                                title: "Success!",
                                text: response.message,
                                icon: "success",
                                timer: 1000,
                                buttons: false
                            });
                            var attribute_Table = $("#tblAttributes").DataTable();
                            attribute_Table.ajax.reload(null, false);
                            $('#addProductAttributeForm').trigger('reset');
                            resetProductAttributeForm();
                        } else {
                            swal("Failed!", response.message, "error");
                            $('#btnSaveProductAttributes').attr('disabled', false);
                        }
                    },
                    error: function(xhr, status, error) {
                        swal("Failed!", response.message, "error");
                    }
                });
            }
        });

    });

    function editProductAttribute(productId, id) {
        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'ProductAttributes', 'action' => 'editProductAttribute']) ?>?product_id=" + productId + "&attr_id=" + id,
            type: 'GET',
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    $('#attribute-status-input').css('display', 'block');
                    $('#hidden-attribute-id').val(response.productAttribute[0].attribute_id);
                    $('#attribute-id').val(response.productAttribute[0].attribute_id).trigger('change');
                    $('#attribute-id').prop('disabled', true);


                    CKEDITOR.instances['ckeditor'].setData(response.productAttribute[0].attribute_description);
                    $('#attribute-status').val(response.productAttribute[0].status);
                    $('#addProductAttributeModalLabel').text("<?= __('Edit Product Attribute') ?>");

                    $('#attributeName').on('optionsLoaded', function() {
                        let attributeValueIds = response.productAttribute.map(attributename => attributename.attribute_value_id);
                        $('#attributeName').val(attributeValueIds).trigger('change');
                        $(this).off('optionsLoaded');
                    });

                    $('#addProductAttributeModal').modal('show');
                } else {
                    swal("Failed!", response.message, "error");
                }
            },
            error: function(xhr, status, error) {
                swal("Failed!", "Error loading data", "error");
            }
        });
    }

    function populateAttributeNameDropdown(attributeId) {
        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'Attributes', 'action' => 'getValuesforAttr']) ?>/" + attributeId,
            type: 'GET',
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            dataType: 'json',
            success: function(response) {
                var attributeValueSelect = $('#attributeName');
                attributeValueSelect.empty();

                attributeValueSelect.append('<option value=""><?= __('Select an Attribute Value') ?></option>');

                $.each(response, function(index, value) {
                    attributeValueSelect.append('<option value="' + value.id + '">' + value.value + '</option>');
                });

                attributeValueSelect.trigger('optionsLoaded');
            },
            error: function() {
                alert('Error loading attribute values.');
            }
        });
    }

    $('#attribute-id').change(function() {
        var attributeId = $(this).val();

        if (attributeId) {
            populateAttributeNameDropdown(attributeId);
        } else {
            $('#attributeName').empty().append('<option value=""><?= __('Select an Attribute Value') ?></option>');
        }
    });



    function resetProductAttributeForm() {
        $('#addProductAttributeForm')[0].reset();
        $('#btnSaveProductAttributes').attr('disabled', false);
        CKEDITOR.instances['ckeditor'].setData('');
        $('#addProductAttributeForm').find('.is-invalid').removeClass('is-invalid');
        $('#addProductAttributeForm').find('.invalid-feedback').hide();
        $('#hidden-attribute-id').val('');
        $('#attribute-status-input').css('display', 'none');
        $('#attributeName').empty().append('<option value=""><?= __('Select an Attribute Value') ?></option>');
        $('#addProductAttributeModalLabel').text("<?= __('Add Product Attribute') ?>");
        $('#attribute-id').prop('disabled', false);
        $('#addProductAttributeModal').modal('hide');
    }

    function viewProductAttribute(product_id, attr_id) {
        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'ProductAttributes', 'action' => 'viewProductAttribute']) ?>/" + product_id + '/' + attr_id,
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    const firstRecord = response.data[0];
                    $('#viewAttributeName').text(firstRecord.attribute.name);
                    $('#viewAttributeDescription').html(firstRecord.attribute_description);

                    let attributeValues = response.data.map(item => item.attribute_value.value);
                    $('#viewAttributeValue').text(attributeValues.join(', '));

                    $('#viewProductAttributeModal').modal('show');
                } else {
                    swal("Failed!", response.message, "error");
                }
            },
            error: function(xhr, status, error) {
                swal("Failed!", "Unable to fetch attribute details.", "error");
            }
        });
    }


    document.addEventListener('DOMContentLoaded', function() {
        function toggleFilterContainer(buttonSelector, containerId) {
            const filterButton = document.querySelector(buttonSelector);
            const filterBodyContainer = document.getElementById(containerId);

            filterButton.addEventListener('click', function(event) {
                event.preventDefault();
                if (filterBodyContainer.classList.contains('showing')) {
                    filterBodyContainer.classList.remove('showing');
                    filterBodyContainer.classList.add('hiding');
                    filterBodyContainer.addEventListener('animationend', function() {
                        filterBodyContainer.classList.remove('hiding');
                        filterBodyContainer.style.display = 'none';
                    }, {
                        once: true
                    });
                } else {
                    filterBodyContainer.style.display = 'block';
                    filterBodyContainer.classList.add('showing');
                }
            });
        }

        toggleFilterContainer('.btn.variant', 'filter-body-container-variant');

        toggleFilterContainer('.btn.relatedprod', 'filter-body-container-relatedprod');

        toggleFilterContainer('.btn.payment', 'filter-body-container-payment');
    });

    var variant_imgformData = new FormData();

    $(document).ready(function() {
        var decimalSeparator = "<?= addslashes($decimalSeparator) ?>";
        var thousandSeparator = "<?= addslashes($thousandSeparator) ?>";
        var currencySymbol = "<?= $currencySymbol ?>";
        var paginationCount = <?= json_encode($paginationCount) ?>;

        var productId = <?= json_encode($product->id) ?>;
        var statusMap = <?= json_encode($statusMap) ?>;

        var variantTable = $("#tblVariants").DataTable({
            ajax: {
                url: "<?= $this->Url->build(['controller' => 'ProductVariants', 'action' => 'getVariantsData']) ?>",
                type: "GET",
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                data: function(d) {
                    d.product_id = productId;
                },
                dataSrc: function(result) {
                    return result.variants;
                }
            },
            columns: [{
                    title: "Variant Name",
                    data: "name"
                }, {
                    title: "Variant Supplier Ref",
                    data: "reference_name"
                },
                {
                    title: "Variant SKU",
                    data: "sku"
                },
                {
                    title: "Variant Size",
                    data: "variant_size"
                },
                {
                    title: "Variant Sales Price",
                    data: "price",
                    render: function(data, type, row) {
                        // Format the price with a formatter function
                        return formatCurrency(data, decimalSeparator, thousandSeparator) + ' ' + currencySymbol;
                    }
                }, {
                    title: "Status",
                    data: "status",
                    render: function(data, type, row) {
                        var statusval = statusMap[data] || {
                            label: "Unknown",
                            class: "label-danger"
                        };
                        return `
                    <div class="badge-outline ${statusval.class}">
                        ${statusval.label}
                    </div>
                `;
                    }
                },
                {
                    title: "Action",
                    data: null,
                    orderable: false,
                    render: function(data, type, row) {
                        let actions = '';

                        <?php if ($canView): ?>
                            actions += `
                        <a href="#" onclick="viewProductVariant(${row.id})"
                            class="" data-bs-toggle="tooltip" title="<?= __('View') ?>">
                            <i class="far fa-eye"></i>
                        </a>`;
                        <?php endif; ?>

                        <?php if ($canEdit): ?>
                            actions += `
                        <a href="#" onclick="editProductVariant(${row.id})"
                            class="" data-bs-toggle="tooltip" title="<?= __('Edit') ?>">
                            <i class="fas fa-pencil-alt"></i>
                        </a>`;
                        <?php endif; ?>

                        <?php if ($canDelete): ?>
                            actions += `
                        <a href="<?= $this->Url->build(['controller' => 'ProductVariants', 'action' => 'deleteProductVariants']) ?>/${row.id}"
                            class="delete-btn" data-bs-toggle="tooltip" title="<?= __('Delete') ?>"
                            data-id="${row.id}"
                            data-delete-confirmation="<?= addslashes($deleteConfirmationMessage); ?>"
                            data-delete-warning="<?= addslashes($deleteWarningMessage); ?>"
                            data-delete-fail="<?= addslashes($deleteFailMessage); ?>">
                            <i class="far fa-trash-alt"></i>
                        </a>`;
                        <?php endif; ?>
                        <?php if ($canEdit): ?>
                            actions += `<a href="javascript:void(0);"
                            class=""
                            data-toggle="tooltip"
                            title="<?= __("Price settings") ?>"
                            data-target="#pricesettings_modal"
                            onclick="loadPriceSettingsModal(${row.id})">
                            <i class="fa fa-money-bill-alt m-r-10"></i>
                            </a>`;
                        <?php endif; ?>


                        return actions;
                    }
                }
            ],
            columnDefs: [{
                orderable: false,
                targets: [-1],
                width: '200px'
            }],
            order: [],
            dom: "rtip",
            pageLength: paginationCount,
            language: {
                infoFiltered: ""
            }
        });


        $("#customSearchBoxVariant").on("keyup", function() {
            variantTable.search(this.value).draw();
        });

        $("#btnResetVariant").on("click", function() {
            variantTable.search('').columns().search('').draw();
        });

        $("#btnFilterVariant").on("click", function(event) {
            event.preventDefault();
            var variantfilter = $("#status-filter").val();
            variantTable.column(4).search(variantfilter, true, false, false).draw();
            variantTable.draw();
        });

        $('#addProductVariantModal').on('hidden.bs.modal', function() {
            resetProductVariantForm();
        });

        $('#variant-image').change(function() {
            var files = this.files;
            validateFiles(
                    files,
                    <?= $ImageSize ?> * 1024 * 1024,
                    <?= json_encode($ImageType) ?>,
                    <?= $ImageMinWidth ?>,
                    <?= $ImageMaxWidth ?>,
                    <?= $ImageMinHeight ?>,
                    <?= $ImageMaxHeight ?>
                )
                .then(function(validFiles) {
                    for (var i = 0; i < validFiles.length; i++) {
                        variant_imgformData.append('variant_media[]', validFiles[i]);
                    }
                    renderVariantMediaPreview(variant_imgformData);
                })
                .catch(function(error) {
                    $('#variant-image').val('');
                    swal('Error', error.message, 'error');
                });
        });

        function renderVariantMediaPreview() {
            $('.web-media-container').empty();
            for (var i = 0; i < variant_imgformData.getAll('variant_media[]').length; i++) {
                var file = variant_imgformData.getAll('variant_media[]')[i];
                var reader = new FileReader();

                reader.onload = (function(file) {
                    return function(e) {
                        var previewHtml = `
                    <div class="variant-image" style="position:relative;">
                        <img src="${e.target.result}" alt="Variant Media Preview" style="max-width:100%; height:auto;">
                        <span class="image-name" title="${file.name}">${file.name}</span>
                        <button type="button" class="delete-img-btn delete-variant-media" data-file-index="${i}">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
                        $('.web-media-container').append(previewHtml);
                    };
                })(file);

                reader.readAsDataURL(file);
            }
        }

        $(document).on('click', '.delete-variant-media', function() {
            var fileIndex = $(this).data('file-index');
            $('#variant-image').val('');
            $('#media-variant-image').val('');
            $(this).closest('.variant-image').remove();
            $(this).closest('.media-variant-image').remove();
        });


        $('#btnSaveProductVariants').click(function(event) {
            event.preventDefault();
            let isValid = true;

            $('#addProductVariantModal').find('input[required], select[required]:visible').each(function() {
                let value = $(this).val().trim();

                if (value === '') {
                    $(this).addClass('is-invalid');
                    let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                    let fieldName = $(this).closest('.form-group').find('label').text().trim().replace(/\*$/, '');
                    feedback.text('<?= __("Please enter ") ?>' + fieldName.toLowerCase() + '.').show();
                    isValid = false;
                } else {
                    $(this).removeClass('is-invalid');
                    let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                    feedback.hide();
                }
            });

            let hasFiles = false;
            if (variant_imgformData.has('variant_media[]')) {
                hasFiles = true;
            }
            let existingImages = $('input[name="existing_variant_images[]"]').map(function() {
                return $(this).val();
            }).get();

            if (existingImages.length > 0) {
                hasFiles = true;
            }
            if (!hasFiles) {
                $('#variant-image').addClass('is-invalid');
                let feedback = $('#variant-image').closest('.form-group').find('.invalid-feedback');
                let fieldName = $('#variant-image').closest('.form-group').find('label').text().trim().replace(/\*$/, '');
                feedback.text('<?= __("Please enter ") ?>' + fieldName.toLowerCase() + '.').show();
                isValid = false;
            } else {
                $('#variant-image').removeClass('is-invalid');
                let feedback = $('#variant-image').closest('.form-group').find('.invalid-feedback');
                feedback.hide();
            }

            if (isValid) {
                $('#btnSaveProductVariants').attr('disabled', true);
                if (CKEDITOR.instances['ckeditor-variant']) {
                    CKEDITOR.instances['ckeditor-variant'].updateElement();
                }

                let formData = new FormData($('#addProductVariantForm')[0]);
                let url = "<?= $this->Url->build(['controller' => 'ProductVariants', 'action' => 'addProductVariants']) ?>";

                if ($('#hidden-variant-id').val()) {
                    let variant_id = $('#hidden-variant-id').val();
                    url = "<?= $this->Url->build(['controller' => 'ProductVariants', 'action' => 'editProductVariants']) ?>/" + variant_id;
                } else {
                    formData.append('status', 'A');
                }

                if (variant_imgformData.has('variant_media[]')) {
                    variant_imgformData.getAll('variant_media[]').forEach(function(file) {
                        formData.append('variant_media[]', file);
                    });
                }

                $.ajax({
                    url: url,
                    type: 'POST',
                    data: formData,
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    processData: false,
                    contentType: false,
                    dataType: 'json',
                    success: function(response) {
                        if (response.status === 'success') {
                            swal({
                                title: "Success!",
                                text: response.message,
                                icon: "success",
                                timer: 1000,
                                buttons: false
                            });
                            var variant_Table = $("#tblVariants").DataTable();
                            variant_Table.ajax.reload(null, false);
                            $('#addProductVariantForm').trigger('reset');
                            resetProductVariantForm();
                        } else {
                            swal("Failed!", response.message, "error");
                        }
                    },
                    error: function(xhr, status, error) {
                        swal("Failed!", response.message, "error");
                    }
                });
            }
        });
    });

    function editProductVariant(id) {
        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'ProductVariants', 'action' => 'editProductVariants']) ?>/" + id,
            type: 'GET',
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            dataType: 'json',
            success: function(response) {
                resetProductVariantForm();
                if (response.status === 'success') {
                    $('#variant-status-input').css('display', 'block');
                    $('#hidden-variant-id').val(response.productVariant.id);
                    $('#variant-name').val(response.productVariant.variant_name);
                    $('#variant-reference-name').val(response.reference_name);
                    $('#variant-sku').val(response.productVariant.sku);
                    $('#variant-size').val(response.productVariant.variant_size);
                    $('#variant-weight').val(response.productVariant.variant_weight || '');
                    // $('#supplier-ref').val(response.productVariant.supplier_id);
                    // $('#variant-prices').val(response.productVariant.purchase_price);
                    $('#variant-quantity').val(response.productVariant.quantity);
                    CKEDITOR.instances['ckeditor-variant'].setData(response.productVariant.variant_description);
                    $('#variant-status').val((response.productVariant.status));

                    let imagesContainer = $('.existing-variant-images');
                    imagesContainer.empty();
                    if (response.productVariantImages && response.productVariantImages.length > 0) {
                        response.productVariantImages.forEach(function(image) {
                            let shortName = image.shortName || image.fileName;
                            imagesContainer.append(`
                            <div class="variant-image-container mt-3" style="position:relative;">
                                <img src="${image.image}" alt="Variant Image"  style="max-width:100%; height:auto;" />
                                <span class="image-name" title="${image.fileName}">${shortName}</span>
                                <input type="hidden" name="existing_variant_images[]" value="${image.id}" />
                                <button type="button" class="delete-img-btn del-ext-variant-image" data-id="${image.id}">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        `);
                        });
                    }
                    $('#addProductVariantModalLabel').text("<?= __('Edit Product Variant') ?>");
                    $('#addProductVariantModal').modal('show');
                } else {
                    swal("Failed!", response.message, "error");
                }
            },
            error: function(xhr, status, error) {
                swal("Failed!", "Error loading data", "error");
            }
        });
    }

    function resetProductVariantForm() {
        variant_imgformData = new FormData();
        $('#addProductVariantForm')[0].reset();
        $('#btnSaveProductVariants').attr('disabled', false);
        CKEDITOR.instances['ckeditor-variant'].setData('');
        $('#addProductVariantForm').find('.is-invalid').removeClass('is-invalid');
        $('#addProductVariantForm').find('.invalid-feedback').hide();
        $('#hidden-variant-id').val('');
        $('#variant-status-input').css('display', 'none');
        $('.web-media-container').empty();
        $('.existing-variant-images').empty();
        $('#addProductVariantModalLabel').text("<?= __('Add Product Variant') ?>");
        $('#addProductVariantModal').modal('hide');
        $('#variant-image').val('');
    }

    function viewProductVariant(attr_id) {
        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'ProductVariants', 'action' => 'viewProductVariants']) ?>/" + attr_id,
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    $('#viewVariantName').text(response.data.variant_name);
                    $('#viewVariantSKU').text(response.data.variant_sku);
                    $('#viewVariantSize').text(response.data.variant_size);
                    $('#viewVariantWeight').text(response.data.variant_weight + ' KG');
                    $('#viewVariantDescription').html(response.data.variant_description);
                    $('#viewProductVariantModal').modal('show');
                } else {
                    swal("Failed!", response.message, "error");
                }
            },
            error: function(xhr, status, error) {
                swal("Failed!", "Unable to fetch varaint details.", "error");
            }
        });
    }

    $(document).on('click', '.del-ext-variant-image', function() {
        var $button = $(this);
        var imageId = $button.data('id');
        var imageType = 'Image';

        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'ProductVariantImages', 'action' => 'deleteImage']) ?>/" + imageId,
            type: 'POST',
            dataType: 'json',
            data: {
                image_type: imageType,
                image_id: imageId
            },
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function(response) {
                if (response.success) {
                    $button.closest('.variant-image-container').remove();
                    swal('Success', response.message, 'success');
                    loadProductVariantImages();
                } else {
                    swal('Failed', response.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                swal('Failed', 'Failed to delete image. Please try again.', 'error');
            }
        });
    });

    var currentImageIndex = 0;
    var images_prev = [];

    function loadProductImages() {
        var productId = <?= $product->id ?>;
        currentImageIndex = 0;
        images_prev = [];
        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'ProductImages', 'action' => 'getImages']) ?>",
            method: 'GET',
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            data: {
                product_id: productId
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    $('#product-images-div').empty();

                    $.each(response.images, function(index, image) {
                        var imgTag = $('<img>')
                            .attr('src', image.src)
                            .attr('alt', 'Product Image')
                            .attr('width', '200')
                            .attr('height', '200')
                            .click(function() {
                                openImageModal(index, 'product');
                            });

                        images_prev.push({
                            src: image.src
                        });

                        var imageName = $('<span>')
                            .addClass('image-name')
                            .attr('title', 'Product Image Name')
                            .text(image.shortName);

                        var deleteButton = '';
                        <?php if (isset($canDelete) && $canDelete): ?>
                            deleteButton = $('<a>')
                                .addClass('delete-img-btn')
                                .attr('data-id', image.id)
                                .attr('data-type', 'product')
                                .attr('href', 'javascript:void(0);')
                                .html('<i class="fas fa-times"></i>')
                                .attr('onclick', 'deleteImage(' + image.id + ')');
                        <?php endif; ?>

                        var radioButton = '';
                        var radioLabel = '';
                        <?php if (isset($canEdit) && $canEdit): ?>
                            radioButton = $('<input>')
                                .attr('type', 'radio')
                                .attr('name', 'default-image')
                                .attr('value', image.id)
                                .attr('onclick', 'makeDefault(' + image.id + ')')
                                .css({
                                    width: '15px',
                                });
                            if (image.default) {
                                radioButton.prop('checked', true);
                            }
                            radioLabel = $('<label>').text('Make Default');
                        <?php endif; ?>

                        $('#product-images-div').append(
                            $('<div>')
                            .addClass('m-5 web-image-container')
                            .css({
                                position: 'relative',
                                display: 'inline-block',
                                width: '200px',
                            })
                            .append(imgTag)
                            .append(imageName)
                            .append(deleteButton)
                            .append(radioButton)
                            .append(radioLabel)
                        );
                    });

                } else {
                    $('#product-images-div').empty();
                    $('#product-images-div').append('<p>No images found for this product.</p>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error: ', error);
                $('#product-images-div').empty();
                $('#product-images-div').append('<p>Error loading images. Please try again later.</p>');
            }
        });
    }

    loadProductImages();

    var currentVariantImageIndex = 0;
    var variantImages_prev = [];

    function loadProductVariantImages() {
        var productId = <?= $product->id ?>;
        currentVariantImageIndex = 0;
        variantImages_prev = [];
        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'ProductVariantImages', 'action' => 'getVariantImages']) ?>",
            method: 'GET',
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            data: {
                product_id: productId
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    $('#variant-images-div').empty();

                    $.each(response.variants, function(variantId, variant) {
                        var variantContainer = $('<div>')
                            .addClass('variant-container')
                            .css({
                                marginBottom: '20px',
                                border: '1px solid #ccc',
                                padding: '10px',
                                borderRadius: '5px'
                            });

                        var variantHeading = $('<h4>')
                            .text(variant.variant_name)
                            .css({
                                marginBottom: '10px'
                            });
                        variantContainer.append(variantHeading);

                        $.each(variant.images, function(index, image) {
                            var imgTag = $('<img>')
                                .attr('src', image.src)
                                .attr('alt', 'Variant Image')
                                .attr('width', '200')
                                .attr('height', '200')
                                .css({
                                    marginRight: '10px',
                                    cursor: 'pointer'
                                })
                                .click(function() {
                                    openImageModal(index, 'variant');
                                });

                            variantImages_prev.push({
                                src: image.src
                            });

                            var imageName = $('<span>')
                                .addClass('image-name')
                                .attr('title', 'Variant Image Name')
                                .text(image.shortName);

                            var deleteButton = '';
                            <?php if (isset($canDelete) && $canDelete): ?>
                                deleteButton = $('<a>')
                                    .addClass('delete-img-btn del-ext-variant-image')
                                    .attr('data-id', image.id)
                                    .attr('data-type', 'variant')
                                    .attr('href', 'javascript:void(0);')
                                    .html('<i class="fas fa-times"></i>')
                                // .attr('onclick', 'deleteImage(' + image.id + ', "variant")');
                            <?php endif; ?>

                            var radioButton = '';
                            var radioLabel = '';
                            <?php if (isset($canEdit) && $canEdit): ?>
                                radioButton = $('<input>')
                                    .attr('type', 'radio')
                                    .attr('name', 'default-variant-image-' + variantId)
                                    .attr('value', image.id)
                                    .attr('onclick', 'makeDefaultVariantImage(' + image.id + ')')
                                    .css({
                                        width: '15px',
                                    });
                                if (image.default) {
                                    radioButton.prop('checked', true);
                                }
                                radioLabel = $('<label>').text('Make Default');
                            <?php endif; ?>

                            variantContainer.append(
                                $('<div>')
                                .addClass('m-5 web-image-container')
                                .css({
                                    position: 'relative',
                                    display: 'inline-block',
                                    width: '200px',
                                })
                                .append(imgTag)
                                .append(imageName)
                                .append(deleteButton)
                                .append(radioButton)
                                .append(radioLabel)
                            );
                        });

                        $('#variant-images-div').append(variantContainer);
                    });

                } else {
                    $('#variant-images-div').empty();
                    $('#variant-images-div').append('<p>No variant images found for this product.</p>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error: ', error);
                $('#variant-images-div').empty();
                $('#variant-images-div').append('<p>Error loading variant images. Please try again later.</p>');
            }
        });
    }

    loadProductVariantImages();



    function openImageModal(index, type = '') {
        currentImageIndex = index;
        if (type == 'variant') {
            $('#modalImage').attr('src', variantImages_prev[currentImageIndex].src);
            $('#prevVarImageBtn').css('display', 'block');
            $('#prevImageBtn').css('display', 'none');
            $('#nextImageBtn').css('display', 'none');
            $('#nextVarImageBtn').css('display', 'block');
        } else {
            $('#modalImage').attr('src', images_prev[currentImageIndex].src);
            $('#prevVarImageBtn').css('display', 'none');
            $('#prevImageBtn').css('display', 'block');
            $('#nextImageBtn').css('display', 'block');
            $('#nextVarImageBtn').css('display', 'none');
        }
        $('#imageViewerModal').modal('show');
        updateButtonState();
    }

    $('#closeModal-image').click(function() {
        $('#imageViewerModal').modal('hide');
        loadProductImages();
        loadProductVariantImages();
    });

    $('#prevImageBtn').click(function() {
        if (currentImageIndex > 0) {
            currentImageIndex--;
            $('#modalImage').attr('src', images_prev[currentImageIndex].src);
            updateButtonState();
        }
    });

    $('#nextImageBtn').click(function() {
        if (currentImageIndex < images_prev.length - 1) {
            currentImageIndex++;
            $('#modalImage').attr('src', images_prev[currentImageIndex].src);
            updateButtonState();
        }
    });

    function updateButtonState() {
        $('#prevImageBtn').attr('disabled', currentImageIndex === 0);
        $('#nextImageBtn').attr('disabled', currentImageIndex === images_prev.length - 1);
    }

    $('#prevVarImageBtn').click(function() {
        if (currentImageIndex > 0) {
            currentImageIndex--;
            $('#modalImage').attr('src', variantImages_prev[currentImageIndex].src);
            updateVarImgButtonState();
        }
    });

    $('#nextVarImageBtn').click(function() {
        if (currentImageIndex < variantImages_prev.length - 1) {
            currentImageIndex++;
            $('#modalImage').attr('src', variantImages_prev[currentImageIndex].src);
            updateVarImgButtonState();
        }
    });

    function updateVarImgButtonState() {
        $('#prevVarImageBtn').attr('disabled', currentImageIndex === 0);
        $('#nextVarImageBtn').attr('disabled', currentImageIndex === variantImages_prev.length - 1);
        console.log(variantImages_prev);
    }

    $('#add-product-images').click(function() {
        $('#product-image-input').click();
    });

    $('#product-image-input').change(function() {
        const files = this.files;
        const formData = new FormData();

        formData.append('product_id', <?= $product->id ?>);
        formData.append('media_type', 'Image');

        validateFiles(
            files,
            <?= $ImageSize ?> * 1024 * 1024,
            <?= json_encode($ImageType) ?>,
            <?= $ImageMinWidth ?>,
            <?= $ImageMaxWidth ?>,
            <?= $ImageMinHeight ?>,
            <?= $ImageMaxHeight ?>
        ).then(function(validFiles) {
            for (let i = 0; i < validFiles.length; i++) {
                formData.append('images[]', validFiles[i]);
            }

            if (validFiles.length > 0) {
                $.ajax({
                    url: "<?= $this->Url->build(['controller' => 'ProductImages', 'action' => 'addMedia']) ?>",
                    type: 'POST',
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        swal("Success!", response.message, "success");
                        loadProductImages();
                        $('#image-input').val('');
                    },
                    error: function(xhr, status, error) {
                        console.error('Error uploading images:', error);
                    }
                });
            } else {
                console.log('No valid files to upload.');
            }
        });
        $('#image-input').val('');
    });

    function deleteImage(imageId) {
        swal({
                title: "Are you sure?",
                text: "Once deleted, you will not be able to recover this media!",
                icon: "warning",
                buttons: true,
                dangerMode: true,
            })
            .then((willDelete) => {
                if (willDelete) {
                    $.ajax({
                        url: "<?= $this->Url->build(['controller' => 'ProductImages', 'action' => 'deleteImage']) ?>/" + imageId,
                        type: 'POST',
                        headers: {
                            'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                        },
                        data: {
                            image_id: imageId
                        },
                        success: function(response) {
                            if (response.success) {
                                swal("Media deleted successfully!", {
                                    icon: "success",
                                });
                                loadProductImages();
                                loadProductVideos();
                            } else {
                                swal("Error!", response.message, "error");
                            }
                        },
                        error: function() {
                            swal("Error!", "There was an issue with the request.", "error");
                        }
                    });
                } else {
                    swal("Your image is safe!");
                }
            });
    }


    function makeDefault(imageId) {
        swal({
                title: "Are you sure?",
                text: "Do you want to make this image the default for the product?",
                icon: "warning",
                buttons: true,
                dangerMode: true,
            })
            .then((willDelete) => {
                if (willDelete) {
                    $.ajax({
                        url: "<?= $this->Url->build(['controller' => 'ProductImages', 'action' => 'makeDefault']) ?>/" + imageId,
                        type: 'POST',
                        headers: {
                            'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                        },
                        data: {
                            image_id: imageId
                        },
                        success: function(response) {
                            if (response.success) {
                                swal("Image set as default successfully!", {
                                    icon: "success",
                                });
                                loadProductImages();
                            } else {
                                swal("Error!", response.message, "error");
                            }
                        },
                        error: function() {
                            swal("Error!", "There was an issue with the request.", "error");
                        }
                    });
                } else {
                    swal("Your image remains unchanged!");
                }
            });
    }

    function makeDefaultVariantImage(imageId) {
        swal({
                title: "Are you sure?",
                text: "Do you want to make this image the default for the product variant?",
                icon: "warning",
                buttons: true,
                dangerMode: true,
            })
            .then((willDelete) => {
                if (willDelete) {
                    $.ajax({
                        url: "<?= $this->Url->build(['controller' => 'ProductVariantImages', 'action' => 'makeDefault']) ?>/" + imageId,
                        type: 'POST',
                        headers: {
                            'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                        },
                        data: {
                            image_id: imageId
                        },
                        success: function(response) {
                            if (response.success) {
                                swal("Image set as default successfully!", {
                                    icon: "success",
                                });
                                loadProductVariantImages();
                            } else {
                                swal("Error!", response.message, "error");
                            }
                        },
                        error: function() {
                            swal("Error!", "There was an issue with the request.", "error");
                        }
                    });
                } else {
                    swal("Your image remains unchanged!");
                }
            });
    }

    $('#add-variant-images').click(function() {
        populateVariantsMediaModal('Image');
    });

    $('#add-variant-videos').click(function() {
        populateVariantsMediaModal('Video');
    });

    function populateVariantsMediaModal(type = '') {
        var productId = <?= $product->id ?>;

        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'ProductVariants', 'action' => 'getVariants']) ?>",
            type: 'GET',
            data: {
                product_id: productId
            },
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    var dropdown = $('#variant-select');
                    dropdown.empty();
                    dropdown.append('<option value="">' + '<?= __('Select Variant') ?>' + '</option>');

                    $.each(response.variants, function(key, value) {
                        dropdown.append('<option value="' + key + '">' + value + '</option>');
                    });

                    var inputField = $('#media-variant-image');
                    var validationMessage = $('#Image-validations-media');

                    if (type == 'Image') {
                        inputField.attr('accept', '<?= implode(", ", $ImageType) ?>');
                        validationMessage.html('<small>Only <?= implode(', ', $productImageTypeDisp) ?> files are allowed. Max size: <?= $ImageSize ?> MB. Dimensions: <?= $ImageMinWidth ?> x <?= $ImageMinHeight ?> and <?= $ImageMaxWidth ?> x <?= $ImageMaxHeight ?>.</small>');
                        $('#variantImageModalLabel').text('<?= __("Upload Variant Images") ?>');
                        $('#saveVariantImages').css('display', 'block');
                        $('#saveVariantVideos').css('display', 'none');
                        $('#variant-select').data('media-type', 'Image');
                    } else if (type == 'Video') {
                        inputField.attr('accept', '<?= implode(", ", $VideoType) ?>');
                        validationMessage.html('<small>Only <?= implode(', ', $productVideoTypeDisp) ?> files are allowed. Max size: <?= $VideoSize ?> MB.</small>');
                        $('#variantImageModalLabel').text('<?= __("Upload Variant Videos") ?>');
                        $('#saveVariantImages').css('display', 'none');
                        $('#saveVariantVideos').css('display', 'block');
                        $('#variant-select').data('media-type', 'Video');
                    }

                    $('#variantImageModal').modal('show');
                } else {
                    swal("Error", response.message, "error");
                }
            },
            error: function() {
                swal("Error", "Failed to fetch variants. Please try again.", "error");
            }
        });
    }


    var media_variant_imgformData = new FormData();
    var media_variant_vidformData = new FormData();

    $('#media-variant-image').change(function() {
        var files = this.files;
        var mediaType = $('#variant-select').data('media-type');

        if (mediaType === 'Image') {
            validateFiles(
                    files,
                    <?= $ImageSize ?> * 1024 * 1024,
                    <?= json_encode($ImageType) ?>,
                    <?= $ImageMinWidth ?>,
                    <?= $ImageMaxWidth ?>,
                    <?= $ImageMinHeight ?>,
                    <?= $ImageMaxHeight ?>
                )
                .then(function(validFiles) {
                    for (var i = 0; i < validFiles.length; i++) {
                        media_variant_imgformData.append('media_variant_images[]', validFiles[i]);
                    }
                    renderMediaVariantMediaPreview(media_variant_imgformData, 'Image');
                })
                .catch(function(error) {
                    $('#media-variant-image').val('');
                    swal('Error', error.message, 'error');
                });
        } else if (mediaType === 'Video') {
            validateFiles(
                    files,
                    <?= $VideoSize ?> * 1024 * 1024,
                    <?= json_encode($VideoType) ?>
                )
                .then(function(validFiles) {
                    for (var i = 0; i < validFiles.length; i++) {
                        media_variant_vidformData.append('media_variant_videos[]', validFiles[i]);
                    }
                    renderMediaVariantMediaPreview(media_variant_vidformData, 'Video');
                })
                .catch(function(error) {
                    $('#media-variant-image').val('');
                    swal('Error', error.message, 'error');
                });
        } else {
            swal('Error', 'Unsupported media type selected.', 'error');
        }
    });


    function renderMediaVariantMediaPreview(formData, type = '') {
        $('.variant-web-media-container').empty();

        if (type == 'Image') {
            for (var i = 0; i < formData.getAll('media_variant_images[]').length; i++) {
                var file = formData.getAll('media_variant_images[]')[i];
                var reader = new FileReader();

                reader.onload = (function(file) {
                    return function(e) {
                        var previewHtml = `
                    <div class="media-variant-image" style="position:relative;">
                        <img src="${e.target.result}" alt="Variant Media Preview" style="max-width:100%; height:auto;">
                        <span class="image-name" title="${file.name}">${file.name}</span>
                        <button type="button" class="delete-img-btn delete-variant-media" data-file-index="${i}">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
                        $('.variant-web-media-container').append(previewHtml);
                    };
                })(file);

                reader.readAsDataURL(file);
            }
        } else if (type == 'Video') {
            for (var i = 0; i < formData.getAll('media_variant_videos[]').length; i++) {
                var file = formData.getAll('media_variant_videos[]')[i];
                var reader = new FileReader();

                reader.onload = (function(file) {
                    return function(e) {
                        var previewHtml = `
                    <div class="media-variant-image" style="position:relative;">
                        <video src="${e.target.result}" alt="Variant Media Preview" style="max-width:200%; height:auto;" controls></video
                        <span class="image-name" title="${file.name}">${file.name}</span>
                        <button type="button" class="delete-img-btn delete-variant-media" data-file-index="${i}" style="right:-165px !important">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
                        $('.variant-web-media-container').append(previewHtml);
                    };
                })(file);

                reader.readAsDataURL(file);
            }
        }
    }

    $('#saveVariantImages').click(function(event) {
        event.preventDefault();
        let isValid = true;

        $('#addVariantImageForm').find('input[required], select[required]:visible').each(function() {
            let value = $(this).val().trim();
            let isSelect2 = $(this).hasClass('select2');
            if (isSelect2) {
                value = $(this).val();
            } else {
                value = $(this).val() ? $(this).val().trim() : '';
            }
            if (value === '') {
                $(this).addClass('is-invalid');
                let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                let fieldName = $(this).closest('.form-group').find('label').text().trim().replace(/\*$/, '');
                feedback.text('<?= __("Please enter ") ?>' + fieldName.toLowerCase() + '.').show();
                isValid = false;
                if (isSelect2) {
                    $(this).closest('.form-group').find('.select2-selection--single').addClass('is-invalid-select');
                }
            } else {
                $(this).removeClass('is-invalid');
                let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                feedback.hide();
                if (isSelect2) {
                    $(this).closest('.form-group').find('.select2-selection--single').removeClass('is-invalid-select');
                }
            }
        });

        if (isValid) {
            $('#saveVariantImages').prop('disabled', true);
            const formData = new FormData($('#addVariantImageForm')[0]);
            if (media_variant_imgformData.has('media_variant_images[]')) {
                media_variant_imgformData.getAll('media_variant_images[]').forEach(function(file) {
                    formData.append('variant_media[]', file);
                });
            }
            $.ajax({
                url: '<?= $this->Url->build(['controller' => 'ProductVariants', 'action' => 'saveVariantImages']); ?>',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        swal({
                            title: "Success!",
                            text: response.message,
                            icon: "success",
                            timer: 1000,
                            buttons: false
                        });
                        $('#variantImageModal').modal('hide');
                        $('#saveVariantImages').prop('disabled', false);
                        loadProductVariantImages();
                        resetVariantImagesForm();

                    } else {
                        $('#variantImageModal').modal('hide');
                        $('#saveVariantImages').prop('disabled', false);
                        swal("Failed!", response.message, "error");
                    }
                },
                error: function(xhr, status, error) {
                    swal("Failed!", response.message, "error");
                }
            });
        }
    });

    $('#saveVariantVideos').click(function(event) {
        event.preventDefault();
        let isValid = true;

        $('#addVariantImageForm').find('input[required], select[required]:visible').each(function() {
            let value = $(this).val().trim();
            let isSelect2 = $(this).hasClass('select2');
            if (isSelect2) {
                value = $(this).val();
            } else {
                value = $(this).val() ? $(this).val().trim() : '';
            }
            if (value === '') {
                $(this).addClass('is-invalid');
                let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                let fieldName = $(this).closest('.form-group').find('label').text().trim().replace(/\*$/, '');
                feedback.text('<?= __("Please enter ") ?>' + fieldName.toLowerCase() + '.').show();
                isValid = false;
                if (isSelect2) {
                    $(this).closest('.form-group').find('.select2-selection--single').addClass('is-invalid-select');
                }
            } else {
                $(this).removeClass('is-invalid');
                let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                feedback.hide();
                if (isSelect2) {
                    $(this).closest('.form-group').find('.select2-selection--single').removeClass('is-invalid-select');
                }
            }
        });

        if (isValid) {
            $('#saveVariantVideos').prop('disabled', true);
            const formData = new FormData($('#addVariantImageForm')[0]);
            if (media_variant_vidformData.has('media_variant_videos[]')) {
                media_variant_vidformData.getAll('media_variant_videos[]').forEach(function(file) {
                    formData.append('variant_media[]', file);
                });
            }
            $('#variantImageModal').modal('hide');
            $('#saveVariantImages').prop('disabled', false);
            showLoadingModal();
            $.ajax({
                url: '<?= $this->Url->build(['controller' => 'ProductVariants', 'action' => 'saveVariantVideos']); ?>',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        swal({
                            title: "Success!",
                            text: response.message,
                            icon: "success",
                            timer: 1000,
                            buttons: false
                        });
                        loadProductVariantVideos();
                        resetVariantImagesForm();

                    } else {
                        $('#variantImageModal').modal('hide');
                        $('#saveVariantImages').prop('disabled', false);
                        swal("Failed!", response.message, "error");
                    }
                },
                error: function(xhr, status, error) {
                    swal("Failed!", response.message, "error");
                }
            });
        }
    });

    function resetVariantImagesForm() {
        $('#addVariantImageForm')[0].reset();
        $('#addVariantImageForm')
            .find('.is-invalid, .is-invalid-select')
            .removeClass('is-invalid is-invalid-select');
        $('#addVariantImageForm')
            .find('.invalid-feedback')
            .hide()
            .text('');
        $('#variant-select').val(null).trigger('change');
        $('.variant-web-media-container').empty();
        media_variant_imgformData = new formData();
        media_variant_vidformData = new formData();
    }

    function loadProductVideos() {
        var productId = <?= $product->id ?>;
        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'ProductImages', 'action' => 'getVideos']) ?>",
            method: 'GET',
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            data: {
                product_id: productId
            },
            dataType: 'json',
            success: function(response) {
                $('#product-videos-div').empty();

                if (response.success && response.videos.length > 0) {
                    $.each(response.videos, function(index, video) {
                        var videoTag = $('<video>')
                            .attr('src', video.src)
                            .attr('alt', 'Product Video')
                            .attr('width', '200')
                            .attr('height', '200')
                            .addClass('video-thumbnail')
                        // .click(function() {
                        //     openVideoModal(index);
                        // });

                        var videoName = $('<span>')
                            .addClass('video-name')
                            .attr('title', 'Product Video Name')
                            .text(video.shortName);

                        var deleteButton = '';
                        <?php if ($canDelete): ?>
                            deleteButton = $('<a>')
                                .addClass('delete-img-btn')
                                .attr('data-id', video.id)
                                .attr('data-type', 'product')
                                .attr('href', 'javascript:void(0);')
                                .html('<i class="fas fa-times"></i>')
                                .attr('onclick', 'deleteImage(' + video.id + ')');
                        <?php endif; ?>

                        $('#product-videos-div').append(
                            $('<div>')
                            .addClass('m-5 web-video-container')
                            .css({
                                position: 'relative',
                                display: 'inline-block',
                                width: '200px',
                            })
                            .append(videoTag)
                            .append(videoName)
                            .append(deleteButton)
                        );
                    });

                    hideLoadingModal();

                } else {
                    hideLoadingModal();
                    $('#product-videos-div').empty();
                    $('#product-videos-div').append('<p>No videos found for this product.</p>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error: ', error);
                $('#product-videos-div').empty();
                $('#product-videos-div').append('<p>Error loading videos. Please try again later.</p>');
            }
        });
    }

    loadProductVideos();

    function loadProductVariantVideos() {
        var productId = <?= $product->id ?>;
        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'ProductVariantImages', 'action' => 'getVariantVideos']) ?>",
            method: 'GET',
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            data: {
                product_id: productId
            },
            dataType: 'json',
            success: function(response) {
                $('#variant-videos-div').empty();

                if (response.success) {
                    $.each(response.variants, function(variantId, variant) {
                        var variantContainer = $('<div>')
                            .addClass('variant-container')
                            .css({
                                marginBottom: '20px',
                                border: '1px solid #ccc',
                                padding: '10px',
                                borderRadius: '5px'
                            });

                        var variantHeading = $('<h4>')
                            .text(variant.variant_name)
                            .css({
                                marginBottom: '10px'
                            });
                        variantContainer.append(variantHeading);

                        if (variant.videos && variant.videos.length > 0) {
                            $.each(variant.videos, function(index, video) {
                                var videoTag = $('<video>')
                                    .attr('src', video.src)
                                    .attr('alt', 'Product Variant Video')
                                    .attr('width', '200')
                                    .attr('height', '200')
                                    .addClass('video-thumbnail')
                                    .css({
                                        marginRight: '10px',
                                        cursor: 'pointer'
                                    })
                                // .click(function() {
                                //     openVideoModal(index, 'variant');
                                // });

                                var videoName = $('<span>')
                                    .addClass('video-name')
                                    .attr('title', 'Product Variant Video Name')
                                    .text(video.shortName);

                                var deleteButton = '';
                                <?php if ($canDelete): ?>
                                    deleteButton = $('<a>')
                                        .addClass('delete-img-btn del-ext-variant-video')
                                        .attr('data-id', video.id)
                                        .attr('data-type', 'variant')
                                        .attr('href', 'javascript:void(0);')
                                        .html('<i class="fas fa-times"></i>');
                                <?php endif; ?>

                                variantContainer.append(
                                    $('<div>')
                                    .addClass('m-5 web-video-container')
                                    .css({
                                        position: 'relative',
                                        display: 'inline-block',
                                        width: '200px',
                                    })
                                    .append(videoTag)
                                    .append(videoName)
                                    .append(deleteButton)
                                );
                            });
                        } else {
                            variantContainer.append('<p>No videos available for this variant.</p>');
                        }

                        $('#variant-videos-div').append(variantContainer);
                    });
                    hideLoadingModal();
                } else {
                    hideLoadingModal();
                    $('#variant-videos-div').append('<p>No variant videos found for this product.</p>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error: ', error);
                $('#variant-videos-div').empty();
                $('#variant-videos-div').append('<p>Error loading videos. Please try again later.</p>');
            }
        });
    }

    loadProductVariantVideos();

    $('#add-product-videos').click(function() {
        $('#product-video-input').click();
    });

    function showLoadingModal() {
        $('#loadingModal').fadeIn();
    }

    function hideLoadingModal() {
        $('#loadingModal').fadeOut();
    }

    $('#product-video-input').change(function() {
        const files = this.files;
        const formData = new FormData();

        formData.append('product_id', <?= $product->id ?>);
        formData.append('media_type', 'Video');
        showLoadingModal();

        validateFiles(
            files,
            <?= $VideoSize ?> * 1024 * 1024,
            <?= json_encode($VideoType) ?>
        ).then(function(validFiles) {
            for (let i = 0; i < validFiles.length; i++) {
                formData.append('videos[]', validFiles[i]);
            }

            if (validFiles.length > 0) {
                $.ajax({
                    url: "<?= $this->Url->build(['controller' => 'ProductImages', 'action' => 'addMedia']) ?>",
                    type: 'POST',
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        swal("Success!", response.message, "success");
                        loadProductVideos();
                        $('#product-video-input').val('');
                    },
                    error: function(xhr, status, error) {
                        console.error('Error uploading videos:', error);
                    }
                });
            } else {
                console.log('No valid videos to upload.');
            }
        });
        $('#product-video-input').val('');
    });

    $(document).on('click', '.video-thumbnail', function() {
        const videoSrc = $(this).attr('src');
        $('#modalVideo').attr('src', videoSrc);
        $('#videoModal').fadeIn();
        $('#modalVideo')[0].play();
    });

    $('#closeModal-video').click(function() {
        $('#videoModal').fadeOut();
        $('#modalVideo')[0].pause();
        $('#modalVideo').attr('src', '');
    });

    $(window).click(function(event) {
        if ($(event.target).is('#videoModal')) {
            $('#videoModal').fadeOut();
            $('#modalVideo')[0].pause();
            $('#modalVideo').attr('src', '');
        }
    });

    $(document).ready(function() {
        var paginationCount = <?= json_encode($paginationCount) ?>;

        var productId = <?= json_encode($product->id) ?>;
        var statusMap = <?= json_encode($statusMap) ?>;
        var RelatedProdTable = $("#tblRelatedProducts").DataTable({
            ajax: {
                url: "<?= $this->Url->build(['controller' => 'RelatedProducts', 'action' => 'getRelatedProducts']) ?>",
                type: "GET",
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                data: function(d) {
                    d.product_id = productId;
                },
                dataSrc: function(result) {
                    return result.related_products;
                }
            },
            columns: [{
                    title: "Related Product ID",
                    data: "id"
                },
                {
                    title: "Product Name",
                    data: "related_product_name"
                },
                {
                    title: "SKU ID",
                    data: "related_product_sku"
                },
                {
                    title: "Supplier Reference Title",
                    data: "related_product_reference_name"
                },
                {
                    title: "Status",
                    data: "status",
                    render: function(data, type, row) {
                        var statusval = statusMap[data] || {
                            label: "Unknown",
                            class: "label-danger"
                        };
                        return `
                <div class="badge-outline ${statusval.class}">
                    ${statusval.label}
                </div>
            `;
                    }
                },
                {
                    title: "Action",
                    data: null,
                    orderable: false,
                    render: function(data, type, row) {
                        let actions = '';
                        <?php if ($canEdit): ?>
                            actions += `
                        <a href="#" onclick="toggleRelatedProdStatus(${row.rel_prod_id})"
                            class="" data-bs-toggle="tooltip" title="<?= __('Change Status') ?>">
                            <i class="fas fa-exchange-alt"></i>
                        </a>`;
                        <?php endif; ?>

                        <?php if ($canDelete): ?>
                            actions += `
                    <a href="<?= $this->Url->build(['controller' => 'RelatedProducts', 'action' => 'deleteRelatedProducts']) ?>/${row.rel_prod_id}"
                        class="delete-btn" data-bs-toggle="tooltip" title="<?= __('Delete') ?>"
                        data-id="${row.id}"
                        data-delete-confirmation="<?= addslashes($deleteConfirmationMessage); ?>"
                        data-delete-warning="<?= addslashes($deleteWarningMessage); ?>"
                        data-delete-fail="<?= addslashes($deleteFailMessage); ?>">
                        <i class="far fa-trash-alt"></i>
                    </a>`;
                        <?php endif; ?>

                        return actions;
                    }
                }
            ],
            columnDefs: [{
                orderable: false,
                targets: [-1],
                width: '200px'
            }],
            order: [],
            dom: "rtip",
            pageLength: paginationCount,
            language: {
                infoFiltered: ""
            }
        });


        $("#customSearchBoxRelated").on("keyup", function() {
            RelatedProdTable.search(this.value).draw();
        });

        $("#btnResetRelated").on("click", function() {
            RelatedProdTable.search('').columns().search('').draw();
        });

        $("#btnFilterRelated").on("click", function(event) {
            event.preventDefault();
            var RelatedProd = $("#related-product-status-filter").val();
            RelatedProdTable.column(4).search(RelatedProd, true, false, false).draw();
            RelatedProdTable.draw();
        });
    });





    function addRelatedProduct() {
        var selectedProducts = $('#product-id').val();
        var productId = <?= json_encode($product->id) ?>;
        if (!selectedProducts || selectedProducts.length === 0) {
            $('#product-id').next('.select2-container').find('.select2-selection--multiple').addClass('is-invalid-select');
        } else {
            $('#addRelatedProductbtn').attr('disabled', true);
            $('#product-id').next('.select2-container').find('.select2-selection--multiple').removeClass('is-invalid-select');
            $.ajax({
                url: '<?= $this->Url->build(["controller" => "RelatedProducts", "action" => "addRelatedProduct"]); ?>',
                type: 'POST',
                data: {
                    product_id: productId,
                    related_id: selectedProducts
                },
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    if (response.status === 'success') {
                        swal({
                            icon: 'success',
                            title: 'Success',
                            text: 'Related product added successfully!',
                        });
                        $('#addRelatedProductbtn').attr('disabled', false);
                        var related_prod_table = $("#tblRelatedProducts").DataTable();
                        related_prod_table.ajax.reload(null, false);
                        $('#product-id').val(null).trigger('change');
                        loadAvailableProducts();
                    } else {
                        swal({
                            icon: 'error',
                            title: 'Error',
                            text: response.message,
                        });
                    }
                },
                error: function() {
                    swal({
                        icon: 'error',
                        title: 'Error',
                        text: 'An error occurred while adding the related product.',
                    });
                }
            });
        }
    }

    function loadAvailableProducts() {
        var productId = <?= json_encode($product->id) ?>;
        $.ajax({
            url: '<?= $this->Url->build(["controller" => "Products", "action" => "getRelatedProductList"]); ?>',
            method: 'GET',
            data: {
                product_id: productId
            },
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function(data) {
                const $select = $('#product-id');
                $select.empty();

                $.each(data, function(value, label) {
                    $select.append(new Option(label, value));
                });

                $select.trigger('change');
            },
            error: function() {
                console.error('Error fetching available products.');
            }
        });
    }

    function toggleRelatedProdStatus(rel_prod_id) {
        swal({
                title: "Are you sure?",
                text: "Do you really want to change the status of this related product?",
                icon: "warning",
                buttons: true,
                dangerMode: true,
            })
            .then((result) => {
                if (result) {
                    $.ajax({
                        url: "<?= $this->Url->build(['controller' => 'RelatedProducts', 'action' => 'toggleRelatedProdStatus']) ?>/" + rel_prod_id,
                        type: 'POST',
                        headers: {
                            'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                        },
                        success: function(response) {
                            if (response.status === 'success') {
                                swal(response.message, {
                                    icon: "success",
                                }).then(() => {
                                    var related_prod_Table = $("#tblRelatedProducts").DataTable();
                                    related_prod_Table.ajax.reload(null, false);
                                });
                            } else {
                                swal("Error!", response.message, "error");
                            }
                        },
                        error: function() {
                            swal("Error!", "There was an issue with the request.", "error");
                        }
                    });
                } else {
                    swal("Your related product status is safe!");
                }
            });
    }

    function formatCurrency(amount, decimalSeparator, thousandSeparator) {
        amount = parseFloat(amount) || 0.00;

        var formatted = amount.toFixed(2);

        var parts = formatted.split('.');

        parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandSeparator);

        return parts.join(decimalSeparator);
    }

    function loadPriceSettingsModal(product_var_id) {
        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'ProductVariants', 'action' => 'getProductPrices']) ?>',
            type: 'POST',
            data: {
                id: product_var_id
            },
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function(response) {
                if (response.status === 'success') {
                    $('#product_var_id').val(product_var_id);
                    $('#sale-price').val(response.data.sales_price || '0.00');
                    $('#promotion-price').val(response.data.promotion_price || '0.00');
                    var decimalSeparator = "<?= addslashes($decimalSeparator) ?>";
                    var thousandSeparator = "<?= addslashes($thousandSeparator) ?>";
                    var currencySymbol = "<?= $currencySymbol ?>";
                    var amount = formatCurrency(response.supplier_latest_price, decimalSeparator, thousandSeparator);
                    $('#latest-purcahse-price').text((amount || '0,00') + ' ' + currencySymbol);
                    $('#latest-normal-reseller-price').text((response.data.normal_reseller_price || '0,00') + ' ' + currencySymbol);
                    $('#latest-large-reseller-price').text((response.data.large_reseller_price || '0,00') + ' ' + currencySymbol);
                    $('#pricesettings_modal').modal('show');
                } else {
                    swal("Error!", response.message || "There was an error fetching the product data.", "error");
                }
            },
            error: function(xhr, status, error) {
                swal("Error!", "There was an error with the request.", "error");
            }
        });
    }

    $('#btnSaveProductPrice').click(function(event) {
        event.preventDefault();
        let isValid = true;

        $('#priceSettingsForm').find('input[required], select[required]:visible').each(function() {
            let value = $(this).val().trim();

            if (value === '') {
                $(this).addClass('is-invalid');
                let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                let fieldName = $(this).closest('.form-group').find('label').text().trim().replace(/\*$/, '');
                feedback.text('<?= __("Please enter ") ?>' + fieldName.toLowerCase() + '.').show();
                isValid = false;
            } else {
                $(this).removeClass('is-invalid');
                let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                feedback.hide();
            }
        });

        if (isValid) {
            $('#btnSaveProductPrice').attr('disabled', true);
            var data = $('#priceSettingsForm').serialize();
            console.log(data);
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'ProductVariants', 'action' => 'addProductPrices']) ?>",
                type: 'POST',
                data: data,
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        swal({
                            title: "Success!",
                            text: response.message,
                            icon: "success",
                            timer: 1000,
                            buttons: false
                        });
                        var variant_Table = $("#tblVariants").DataTable();
                        variant_Table.ajax.reload(null, false);
                        $('#pricesettings_modal').modal('hide');
                        $('#btnSaveProductPrice').attr('disabled', false);
                    } else {
                        swal("Failed!", response.message, "error");
                    }
                },
                error: function(xhr, status, error) {
                    console.log(error);
                    swal("Failed!", response.message, "error");
                }
            });
        }
    });
</script>
<?php $this->end(); ?>